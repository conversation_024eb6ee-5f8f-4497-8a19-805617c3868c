# Trackers

1. Steps
2. Sleep
3. Activity
   1. Walk
   2. Run
   3. Bike
4. Weight
5. Food
6. Glucose
7. Heart Rate
8. Blood Pressure
9. Pulse Oximeter
10. Water
11. Height
12. Body measurements

blood_pressure – Holds blood pressure numbers (systolic and diastolic).
blood_glucose – Records blood glucose levels.

Activity
https://dev.fitbit.com/build/reference/web-api/activity/get-activity-log-list/

Activity Summary - Day
https://dev.fitbit.com/build/reference/web-api/activity/get-daily-activity-summary/

Sleep
https://dev.fitbit.com/build/reference/web-api/sleep/get-sleep-log-list/

Weight, BMI, Fat
https://dev.fitbit.com/build/reference/web-api/body/get-weight-log/

Heart Rate - Day
https://dev.fitbit.com/build/reference/web-api/heartrate-timeseries/get-heartrate-timeseries-by-date/

PulseOx - Day
No permission to the app
https://dev.fitbit.com/build/reference/web-api/spo2/get-spo2-summary-by-date/

Water - Day
https://dev.fitbit.com/build/reference/web-api/nutrition/get-water-log/

This application does not have permission to READ OXYGEN_SATURATION data. Visit https://dev.fitbit.com/docs/oauth2 for more information on the Fitbit Web API authorization process.

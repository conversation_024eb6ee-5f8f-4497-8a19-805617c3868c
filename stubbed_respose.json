{"success": true, "message": "All trigger logs for user: 7ad86571-5d9a-4a7b-ace0-fbf6946fd540 for duration: 15", "data": [{"startedAt": "2024-01-19T13:56:40.567Z", "triggers": [{"mealLogId": "65aa7f9734dd1317cac9c4b8", "mealTags": [{"grade": "B", "tagName": "apple", "nutriScore": 0, "imageUrl": "https://twentydeg-dev.s3.amazonaws.com/users/b12b1963-9931-4c3b-a7d4-8c74b5d71f59/meallogs/cropped/a6e33bc0-8dc4-430e-8bcd-e9227ec98b68.png?AWSAccessKeyId=ASIAQYJVGNOHZSN5GHWG&Expires=1750361988&Signature=%2BJRYZh2Ud2eXlEkA66SMjg3OZnk%3D&X-Amzn-Trace-Id=Root%3D1-6854131c-2190a9374d5b653443de294f%3BParent%3D2fd1afce87054554%3BSampled%3D1%3BLineage%3D1%3Ab841adda%3A0&x-amz-security-token=IQoJb3JpZ2luX2VjEL7%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJHMEUCIGEr6AiUt5G7MhC6CF4zrM1CQS%2FXzJuUb4Zxme3dWHyPAiEAmUr4V2P9sHulbpLE%2Fyk6o1u%2BBO1l12a6%2BcniSluEvXkqjwMIp%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgwwNTIxODgwNDgyNzEiDOvYsvtkoCqsYjb%2B%2BSrjAgpiTL5%2BfiMls%2F5USEtnp2ooyA%2BG7SbYUhxpqi6lidlyjf4feb7ErTH%2B0vmkrge6PTgj5uyvlt6ol0EsK9JnhEmQ6RjI%2FeKhYBIqdfD0u6aw4T4rxE1StjRFZOmcl9pCVbbxNV0IyN72used2s704bhvZmsaUrRf96i24whuFcAlGf6MNs5VbKDBgSmkPeRyo64peoxFp4yzjHkV%2B%2Fh2m29h5hgdxPLdwIIhaiFNgvUkkPwbkGfJhcS6SCJ%2FWDwgObf3PkijenXY02XOfudpNc9syggPfsToh8CNAZfqmnrpZko4792XXOMv4ePuZKYPwBqj6dWOperTVlayR8dWiY29qzOP61r%2Bj%2B%2FGKEONk19%2FPpio72P7GKoxEDsTX6yG8zK8B8llwIp7rqsEAXlm8PqK%2FKpi0oqg4jW1pjSw%2FsXabSHEB73XS%2BRj7QNR1%2FDLoHyXZ2hWXqRh7ao0YV%2BQ0qKlu0wnqbQwgY6ngFLS7YhykpNpHrsPGcGIUknFX%2FzjSPQ6kyPVEtUpi31%2FqlpT5YyJVSJJKE0uuFNzIQ56ED87hQdsbKFbHN6WSmTSpz%2F5BsYysyzWLagUCa2eqeXZqheOz3B7tVdzCM%2Fysmd7atxUcR1abmSGskE6CITRewapZE2gk2XSwldh1e5CdCjGxfos7zUItIfb4B9en8lduTVN89LOdFcrCiaBg%3D%3D", "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}], "triggeredAt": "2024-01-19T13:56:40.567Z", "graphIconURL": "https://example.com/default_icon.png"}], "details": {"peakCGM": 145, "unit": "mg/dl", "aboveTargetSecs": 5100, "score": {"final": 6.62, "total": 66.21, "breakdown": {"delta": 0, "auc": 54.94, "recovery": 0, "cv": 11.27}}}, "expiresAt": "2024-01-19T15:56:40.567Z", "_id": "IdkCIo0BumX87aigbqEl"}, {"metadata": [{"eventId": "wNn39IwBumX87aigcqDw", "eventType": "Walking", "timestamp": "2024-01-11T12:29:31.716Z", "imageUrl": "https://twentydeg-dev.s3.amazonaws.com/users/b12b1963-9931-4c3b-a7d4-8c74b5d71f59/meallogs/cropped/a6e33bc0-8dc4-430e-8bcd-e9227ec98b68.png?AWSAccessKeyId=ASIAQYJVGNOHZSN5GHWG&Expires=1750361988&Signature=%2BJRYZh2Ud2eXlEkA66SMjg3OZnk%3D&X-Amzn-Trace-Id=Root%3D1-6854131c-2190a9374d5b653443de294f%3BParent%3D2fd1afce87054554%3BSampled%3D1%3BLineage%3D1%3Ab841adda%3A0&x-amz-security-token=IQoJb3JpZ2luX2VjEL7%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJHMEUCIGEr6AiUt5G7MhC6CF4zrM1CQS%2FXzJuUb4Zxme3dWHyPAiEAmUr4V2P9sHulbpLE%2Fyk6o1u%2BBO1l12a6%2BcniSluEvXkqjwMIp%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FARAAGgwwNTIxODgwNDgyNzEiDOvYsvtkoCqsYjb%2B%2BSrjAgpiTL5%2BfiMls%2F5USEtnp2ooyA%2BG7SbYUhxpqi6lidlyjf4feb7ErTH%2B0vmkrge6PTgj5uyvlt6ol0EsK9JnhEmQ6RjI%2FeKhYBIqdfD0u6aw4T4rxE1StjRFZOmcl9pCVbbxNV0IyN72used2s704bhvZmsaUrRf96i24whuFcAlGf6MNs5VbKDBgSmkPeRyo64peoxFp4yzjHkV%2B%2Fh2m29h5hgdxPLdwIIhaiFNgvUkkPwbkGfJhcS6SCJ%2FWDwgObf3PkijenXY02XOfudpNc9syggPfsToh8CNAZfqmnrpZko4792XXOMv4ePuZKYPwBqj6dWOperTVlayR8dWiY29qzOP61rL2j%2B%2FGKEONk19%2FPpio72P7GKoxEDsTX6yG8zK8B8llwIp7rqsEAXlm8PqK%2FKpi0oqg4jW1pjSw%2FsXabSHEB73XS%2BRj7QNR1%2FDLoHyXZ2hWXqRh7ao0YV%2BQ0qKlu0wnqbQwgY6ngFLS7YhykpNpHrsPGcGIUknFX%2FzjSPQ6kyPVEtUpi31%2FqlpT5YyJVSJJKE0uuFNzIQ56ED87hQdsbKFbHN6WSmTSpz%2F5BsYysyzWLagUCa2eqeXZqheOz3B7tVdzCM%2Fysmd7atxUcR1abmSGskE6CITRewapZE2gk2XSwldh1e5CdCjGxfos7zUItIfb4B9en8lduTVN89LOdFcrCiaBg%3D%3D", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}, {"eventId": "wNn39IwBumX87aigcqDw", "eventType": "sleep", "timestamp": "2024-01-11T12:31:31.716Z", "duration": 23940, "details": {"deep": 12009, "rem": 20019, "awake": 3921, "light": 9920}, "graphIconURL": "https://example.com/default_icon.png"}], "startedAt": "2024-01-11T16:21:06.289Z", "triggers": [{"mealLogId": "6595cf8370c7793ef009bf7d", "mealTags": [{"grade": "C", "tagName": "milkshake", "nutriScore": 4, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}, {"grade": "A", "tagName": "apple", "nutriScore": 1, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}], "triggeredAt": "2024-01-11T16:21:06.289Z", "graphIconURL": "https://example.com/default_icon.png"}], "details": {"peakCGM": 145, "unit": "mg/dl", "aboveTargetSecs": 5100, "score": {"final": 6.62, "total": 66.21, "breakdown": {"delta": 0, "auc": 54.94, "recovery": 0, "cv": 11.27}}}, "expiresAt": "2024-01-11T18:21:06.289Z", "_id": "29lU-YwBumX87aigR6A6"}, {"startedAt": "2024-01-11T16:04:01.275Z", "triggers": [{"mealLogId": "65a0116dc255eb98b3bbabf0", "mealTags": [{"grade": "C", "tagName": "eggs", "nutriScore": 3, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}, {"grade": "A", "tagName": "milk and milk products", "nutriScore": -1, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}], "graphIconURL": "https://example.com/default_icon.png", "triggeredAt": "2024-01-11T16:04:01.275Z"}, {"mealLogId": "65a011a5c255eb98b3bbabfa", "mealTags": [], "triggeredAt": "2024-01-11T16:08:41.774Z", "graphIconURL": "https://example.com/default_icon.png"}], "details": {"peakCGM": 145, "unit": "mg/dl", "aboveTargetSecs": 5100, "score": {"final": 6.62, "total": 66.21, "breakdown": {"delta": 0, "auc": 54.94, "recovery": 0, "cv": 11.27}}}, "expiresAt": "2024-01-11T18:08:41.774Z", "_id": "2tlE-YwBumX87aigd6B6"}, {"metadata": [{"eventId": 1, "eventType": "Push-ups", "timestamp": "2024-01-11T13:26:01.587Z", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}, {"eventId": 1, "eventType": "Squats", "timestamp": "2024-01-11T13:26:01.587Z", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}, {"eventId": 1, "eventType": "Walking", "timestamp": "2023-10-26T15:42:58.112Z", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}], "startedAt": "2024-01-11T13:21:51.669Z", "triggers": [{"mealLogId": "6595cf8370c7793ef009bf7d", "mealTags": [{"grade": "C", "tagName": "milkshake", "nutriScore": 4, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}, {"grade": "A", "tagName": "apple", "nutriScore": 1, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}], "graphIconURL": "https://example.com/default_icon.png", "triggeredAt": "2024-01-11T13:21:51.669Z"}], "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "expiresAt": "2024-01-11T15:21:51.669Z", "_id": "0tmv-IwBumX87aigzqBJ"}, {"metadata": [{"eventId": "wNn39IwBumX87aigcqDw", "eventType": "Walking", "timestamp": "2024-01-11T10:57:32.732Z", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}, {"eventId": "wNn39IwBumX87aigcqDw", "eventType": "Walking", "timestamp": "2024-01-11T12:29:31.716Z", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}, {"eventId": "wNn39IwBumX87aigcqDw", "eventType": "Walking", "timestamp": "2024-01-11T12:31:31.716Z", "details": {"totalDuration": 300, "sets": [{"setDuration": 300, "repCount": 10, "setScore": 80}], "calories": null, "totalScore": 88}, "graphIconURL": "https://example.com/default_icon.png"}], "startedAt": "2024-01-11T10:43:17.869Z", "triggers": [{"mealLogId": "6595cf8370c7793ef009bf73E534", "mealTags": [], "triggeredAt": "2024-01-11T10:53:04.295Z"}, {"mealLogId": "6595cf8370c7793ef009bf7d", "mealTags": [{"grade": "A", "tagName": "apple", "nutriScore": 1, "computedNutritions": {"cholesterol": {"label": "Cholesterol", "unit": "mg", "quantity": 0}, "protein": {"label": "<PERSON><PERSON>", "unit": "g", "quantity": 15.8}, "calories": {"label": "Calories", "unit": "kcal", "quantity": 354}, "carbohydrates": {"label": "Carbohydrates", "unit": "g", "quantity": 56}, "fat": {"label": "Fat", "unit": "g", "quantity": 5.2}}}], "triggeredAt": "2024-01-11T10:53:04.295Z", "graphIconURL": "https://example.com/default_icon.png"}], "details": {"peakCGM": 145, "unit": "mg/dl", "aboveTargetSecs": 5100, "score": {"final": 6.62, "total": 66.21, "breakdown": {"delta": 0, "auc": 54.94, "recovery": 0, "cv": 11.27}}}, "expiresAt": "2024-01-11T12:53:04.295Z", "_id": "ydkg-IwBumX87aighqCC"}]}
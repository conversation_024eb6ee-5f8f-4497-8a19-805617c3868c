const express = require("express");
const bodyParser = require("body-parser");
const serverless = require('serverless-http');
const nconf = require("nconf");
const routes = require("./controllers/index");
const { log: logger } = require("./utils/logger");
const { config } = require("./environment/index");
const { getClient } = require("./utils/connection");
const { getStaticData } = require("./utils/staticData");
const app = express();

// Move clientInitialized to module level
let clientInitialized = false;

app.use(express.json({ limit: '2mb' }));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true, limit: '2mb' }));

// Request Logging Middleware
app.all("/*", (req, res, next) => {
  const obj = {
    method: req.method,
    url: req.originalUrl,
    headers: req.headers,
  };
  if (req.query) {
    obj.query = req.query;
  }
  if (req.body) {
    obj.body = req.body;
  }
  const url = obj?.url || "path not found";
  logger.info(obj, "API request received, Path: " + url);
  
  // Add response completion logging
  res.on('finish', () => {
    logger.info({
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode
    }, "Request completed, Path: " + url);
  });

  next();
});

app.use("/trackers", routes.router);
app.use("/trackers/api/v1", routes.authenticatedRouter);

// Custom Error handler middleware
app.use((err, req, res, next) => {
  logger.warn("Error occurred in API handler", { url: req.originalUrl, err });

  const statusCode = err.statusCode || 500;
  const message = err.statusCode
    ? err.message
    : "Internal Error: Something went wrong";
  res.status(statusCode).json({
    success: false,
    message,
    ...(err.error ? {error: err.error} : {}),
  });
});

const env = nconf.get('NODE_ENV') || 'development'

if (env == 'development') {
  const server = app.listen(config.PORT, async () => {
    try {

      process.env['AWS_ACCESS_KEY_ID'] = "********************";
      process.env['AWS_SECRET_ACCESS_KEY'] = "BtH1XFJC4MNa2ntsTSpWo7sx1hKl/iW9ghqaIrm2";
      
      await getStaticData();
      await getClient();
      logger.info(`🚀 Listening on port ${config.PORT}`);
    } catch (error) {
      logger.fatal(error);
      server.close();
    }
  });
} else {
  // Initialize resources before first request
  const initializeResources = async () => {
    if (!clientInitialized) {
      try {
        const initStartTime = Date.now();
        logger.info('Starting Lambda initialization');

        // Parallelize static data fetching and OpenSearch client creation
        const [staticDataResult, clientResult] = await Promise.all([
          (async () => {
            const staticDataStartTime = Date.now();
            const result = await getStaticData();
            const staticDataDuration = Date.now() - staticDataStartTime;
            logger.info(`Static data fetched in ${staticDataDuration}ms`);
            return result;
          })(),
          (async () => {
            const clientStartTime = Date.now();
            const result = await getClient();
            const clientDuration = Date.now() - clientStartTime;
            logger.info(`OpenSearch client created in ${clientDuration}ms`);
            return result;
          })()
        ]);

        const totalDuration = Date.now() - initStartTime;
        logger.info(`AWS Lambda resources initialized successfully in ${totalDuration}ms`);
        clientInitialized = true;  // Move inside try block
      } catch (error) {
        clientInitialized = false;  // Reset on failure
        logger.error('Failed to initialize Lambda resources', error);
        throw error;
      }
    }
  };

  // Wrap the app with initialization
  const handler = async (event, context) => {
    // Configure Lambda context for faster termination
    context.callbackWaitsForEmptyEventLoop = false;

    await initializeResources();
    return await serverless(app)(event, context);
  };

  module.exports.handler = handler;
}

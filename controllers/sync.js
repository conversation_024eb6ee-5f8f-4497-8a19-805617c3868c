const { log: logger } = require("../utils/logger");
const { sendSQSMessage, sendTargetComputationSQSMessage } = require("../utils/aws");
const { userService } = require("../service/users");
const { getStaticSources } = require("../utils/staticData");

module.exports.hourlySync = async function (req, res, next) {
  try {
    const sources = await getStaticSources();
    logger.info(`hourlySync() started: ${new Date().toISOString()}`);
    const fitbitUsers = await userService.getAllUsers(sources.Fitbit.id);
    const currentHourFitbitUsers = getCurrentHourUsers(fitbitUsers);

    const dexcomUsers = await userService.getAllUsers(sources.Dexcom.id);
    const currentHourDexcomUsers = getCurrentHourUsers(dexcomUsers);

    const ouraUsers = await userService.getAllUsers(sources.Oura.id);
    const currentHourOuraUsers = getCurrentHourUsers(ouraUsers);

    logger.info(`currentHourFitbitUsers: ${JSON.stringify(currentHourFitbitUsers)}`);
    logger.info(`currentHourDexcomUsers: ${JSON.stringify(currentHourDexcomUsers)}`);
    logger.info(`currentHourOuraUsers: ${JSON.stringify(currentHourOuraUsers)}`);

    logger.info(`Calling pushUsersInSQS()`);
    const { successfulUsers: successfulFitbitUsers, failedUsers: failedFitbitUsers } = await pushUsersInSQS(currentHourFitbitUsers, sources.Fitbit.name);
    const { successfulUsers: successfulDexcomUsers, failedUsers: failedDexcomUsers } = await pushUsersInSQS(currentHourDexcomUsers, sources.Dexcom.name);
    const { successfulUsers: successfulOuraUsers, failedUsers: failedOuraUsers } = await pushUsersInSQS(currentHourOuraUsers, sources.Oura.name);
    logger.info(`successfulFitbitUsers: ${JSON.stringify(successfulFitbitUsers)}`);
    logger.warn(`failedFitbitUsers: ${JSON.stringify(failedFitbitUsers)}`);
    logger.info(`successfulDexcomUsers: ${JSON.stringify(successfulDexcomUsers)}`);
    logger.warn(`failedDexcomUsers: ${JSON.stringify(failedDexcomUsers)}`);
    logger.info(`successfulOuraUsers: ${JSON.stringify(successfulOuraUsers)}`);
    logger.warn(`failedOuraUsers: ${JSON.stringify(failedOuraUsers)}`);
    return res.status(200).json({
      success: true,
      message: "Data synced",
      users: {
        fitbitUsers: currentHourFitbitUsers.map(user => user.userId),
        dexcomUsers: currentHourDexcomUsers.map(user => user.userId),
        ouraUsers: currentHourOuraUsers.map(user => user.userId)
      }
    });
  } catch (error) {
    logger.error(`hourlySync error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
};

function getCurrentHourUsers(users) {
  const usersInCurrentHours = [];
  const currentHour = new Date().getHours();
  for (let user of users) {
    const userCreatedAtHour = new Date(user.createdAt).getHours();
    if (userCreatedAtHour === currentHour && user.lastSynced) 
      usersInCurrentHours.push({
        userId: user.userId,
      });
  }
  return usersInCurrentHours;
}

async function pushUsersInSQS(userList, sourceName) {
  const successfulUsers = [];
  const failedUsers = [];
  for (const user of userList) {
    // sourceName will be used to construct sync endpoint
    const success = await sendSQSMessage(JSON.stringify({ userId: user.userId, sourceName }));
    if (success) {
      successfulUsers.push(user.userId);
    } else {
      failedUsers.push(user.userId);
    }

    // Sending data in target computation SQS, to send nudges to inactive users
    const targetComputationSQSMsg = JSON.stringify({
      userId: user.userId,
      date: '',
      trackerIdDeviceIdMapping: {},
      time: new Date().getTime(), // unique identifier
      metadata: {
        isCron: true
      },
    });
    await sendTargetComputationSQSMessage(targetComputationSQSMsg);
  }

  return { successfulUsers, failedUsers };
}

const { asyncHandler, deepCopy, getAdjustedDate, getLocalDateTime, getUTCDateTimeByLocalDate, getDuration, getRequestedLanguage, getLanguageText, getFileExtension, getFileType } = require("../utils/helpers");
const { log: logger} = require("../utils/logger");
const { config } = require("../environment/index");
const targetCronService = require("../service/targetCron");
const targetAchievedService = require("../service/targets_achieved");
const targetService = require("../service/targets");
const { getLogsByDateRange: getExerciseLogsByDateRange } = require("../service/exerciseLogs");
const { getLogsByDateRange: getMindfulnessLogsByDateRange } = require("../service/mindfulnessLogs");
const { devicesService } = require("../service/devices");
const { trackersService } = require("../service/trackers");
const { getUTCOffsetValue } = require("../service/userProfile");
const wellnessStaticData = require("../service/wellness.json");
const { upsertWellnessScore } = require("../service/wellness_score");

const REQUIRED_TARGETS_MET = 3;
const REQUIRED_ENTRIES = 1;
const DATA_CAPTURE_TRACKERIDS = [4, 5]; // Activity & Sleep only
const CONSISTENCY_TARGETIDS = [4, 10, 11, 15]; // Sleep, Activity, Mindfulness & MealLog
const GOAL_MAX_SCORE = 40;
const MAX_TOTAL_SCORE = 100;
const shiftActiveWindow = -1;
const scoreGifs = config.wellnessScore.media.gifs;

const getWellnessScore = asyncHandler(async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded?.user_guid;
    const reqLanguage = getRequestedLanguage(req);

    const data = deepCopy(wellnessStaticData);
    const UTCOffsetMin = await getUTCOffsetValue(userId);
    const activeWindowData = await targetCronService.getData(userId);
    const { activeWindowStartDate, activeWindowEndDate } = activeWindowData || {};
    if(!activeWindowStartDate || !activeWindowEndDate) {
      return next({ message: "No active window found", statusCode: 403 });
    }

    const wellnessScoreDetails = await getWellnessScoreWithDetails(userId, activeWindowStartDate, activeWindowEndDate, UTCOffsetMin, data.details);
    // Allocating score corresponding details object
    wellnessScoreDetails.forEach(item => {
      const idComponents = item.id.split('.');
      const detailId = parseInt(idComponents[0]);
      const subDetailId = idComponents.length > 1 ? parseInt(idComponents[1]) : null;
    
      const detail = data.details.find(detail => detail.id === detailId);
      if (detail) {
        if (subDetailId !== null && subDetailId != 0) {
          const subDetail = detail.details.find(subDetail => subDetail.id === subDetailId);
          if (subDetail) {
            subDetail.score = item?.score || 0;
            detail.score += item?.score;
          }
        } else {
          detail.score = item?.score || 0;
        }
      }
    });
    const totalScore = Math.min(data.details.reduce((acc, detail) => acc + detail?.score || 0, 0), MAX_TOTAL_SCORE);
    data.score = totalScore;

    const wellnessScoreDoc = getWellnessScoreDoc(userId, totalScore);
    await upsertWellnessScore(userId, wellnessScoreDoc);

    const imageUrls = getGifByScore(totalScore);
    data.content.media.imageUrl = imageUrls;
    data.content.media.type = getFileType(getFileExtension(data.content.media.imageUrl.light));
    data.content.media.mediaOverlayText = totalScore.toString();

    data.content.title = getLanguageText('recommendation_title_learn_more', reqLanguage);
    data.content.subtitle = getLanguageText('recommendation_subtitle_learn_more', reqLanguage);
    data.content.primaryAction.title = getLanguageText('recommendation_primary_action_title_learn_more', reqLanguage);

    const startDate = getAdjustedDate(activeWindowStartDate, shiftActiveWindow * 7);
    const endDate = getAdjustedDate(activeWindowEndDate, shiftActiveWindow * 7);
    const wellnessScoreData = { ...data, startDate, endDate };
    return res.status(200).json({ success: true, message: `Wellness score for userId: ${userId}`, data: wellnessScoreData });
  } catch (error) {
    logger.error(`getWellnessScore error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
});

async function getWellnessScoreWithDetails(userId, activeWindowStartDate, activeWindowEndDate, UTCOffsetMin, detailsObj) {
  const startDate = getAdjustedDate(activeWindowStartDate, shiftActiveWindow * 7);
  const endDate = getAdjustedDate(activeWindowEndDate, shiftActiveWindow * 7);
  const wellnessIds = getWellnessIds(detailsObj);
  for (const scoreItem of wellnessIds) {
    const { id, targetId } = scoreItem;
    switch (id) {
      case '1.0': { // Data Capture
        scoreItem.score = await getDataCaptureScore(userId, DATA_CAPTURE_TRACKERIDS);
        break;
      }
      case '2.3': { // Consistency
        scoreItem.score = await getConsistencyScore(userId, startDate, endDate, CONSISTENCY_TARGETIDS, REQUIRED_TARGETS_MET);
        break;
      }
      case '2.4': { // Food
        scoreItem.score = await getPlatformSupportedScore(userId, startDate, endDate, targetId, REQUIRED_ENTRIES, UTCOffsetMin);
        break;
      }
      case '2.5': { // Exercise
        scoreItem.score = await getPlatformSupportedScore(userId, startDate, endDate, targetId, REQUIRED_ENTRIES, UTCOffsetMin);
        break;
      }
      case '2.6': { // Stress
        scoreItem.score = await getPlatformSupportedScore(userId, startDate, endDate, targetId, REQUIRED_ENTRIES, UTCOffsetMin);
        break;
      }
      case '2.7': { // Additional targets
        scoreItem.score = await getAddionalTargetsScore(userId, startDate, endDate, REQUIRED_TARGETS_MET);
        break;
      }
      case '8.0': { // Progress
        scoreItem.score = await getGoalScore(userId, UTCOffsetMin);
        break;
      }
      default: {
        scoreItem.score = 0;
      }
    }
  }
  return wellnessIds;
}

async function getDataCaptureScore(userId, reqTrackerIds) {
  const { defaultDevices } = await trackersService.getAllDefaultTrackers(userId);
  const { connectedDevices } = await devicesService.getConnectedDevices(userId);
  for(let trackerId of reqTrackerIds) {
    const defaultDeviceId = defaultDevices?.find(dev => dev?.trackerId === trackerId)?.deviceId;
    // default device not found
    if(!defaultDeviceId) {  
      return 0;
    }
    const connectedDevice = connectedDevices?.find(deviceData => deviceData?.id == defaultDeviceId &&  deviceData.isConnected == true);
    const connectedTrackers = connectedDevice?.trackers || [];
    const isEnabled = connectedTrackers.find(tracker => (tracker.trackerId == trackerId))?.isEnabled || false; 
    // default device found, but it mey be disconnected or tracker may not be enabled 
    if(!isEnabled) {
      return 0;
    }
  }
  return 20;
}

async function getConsistencyScore(userId, startDate, endDate, targetIds, noOfRequiredTargetsMet) {
  const targetAchievementsData = await targetAchievedService.getTargetsAchievedByIsMet(userId, startDate, endDate, targetIds, true, false);
  const targetAchievementsPerTargetId = targetIds.reduce((acc, targetId) => ({ ...acc, [targetId]: 0 }), {});

  for (const targetAchievementDoc of targetAchievementsData) {
    const targetId = targetAchievementDoc.targetId;
    targetAchievementsPerTargetId[targetId]++;
  }
  const isMetPerWeek =
    (Object.values(targetAchievementsPerTargetId)?.filter(
      (metCount) => metCount < noOfRequiredTargetsMet
    )?.length > 0) ? false : true;

  return isMetPerWeek ? 10 : 0;
}

async function getPlatformSupportedScore(userId, startDate, endDate, targetId,  noOfRequiredEntries = 1, UTCOffsetMin) {
  if(!targetId) return 0;
  
  try {
    let noOfLogEntries = 0;
    const miliSecInDay = 24 * 60 * 60 * 1000;
    switch(targetId) {
      case 10: {
        const startTime = getUTCDateTimeByLocalDate(startDate, UTCOffsetMin);
        const endTime = getUTCDateTimeByLocalDate(new Date(endDate).getTime() + miliSecInDay - 1, UTCOffsetMin);
        // Fetching platform supported entries only, exerciseId != -1
        const logEntires = await getExerciseLogsByDateRange(userId, startTime, endTime, true, true)
        noOfLogEntries = logEntires?.length || 0;
        break;
      }
      case 11: {
        const startTime = getUTCDateTimeByLocalDate(startDate, UTCOffsetMin);
        const endTime = getUTCDateTimeByLocalDate(new Date(endDate).getTime() + miliSecInDay - 1, UTCOffsetMin);
        // Fetching platform supported entries only, mindfulnessId != -1
        const logEntires = await getMindfulnessLogsByDateRange(userId, startTime, endTime, true, true)
        noOfLogEntries = logEntires?.length || 0;
        break;
      }
      case 15: {
        // Rather than calling mealLog service (requires MongoDB connection), we can call target achievement for mealLog, as it is supported by platform only
        const targetAchivementEntries = await targetAchievedService.getTargetsAchievedByIsMet(userId, startDate, endDate, [targetId], true, false)
        noOfLogEntries = targetAchivementEntries?.length || 0;
        break;
      }
      default: {
        noOfLogEntries = 0;
      }
    }

    return (noOfLogEntries >= noOfRequiredEntries) ? 10 : 0;
  }
  catch(error){
    logger.error(`Error in getPlatformSupportedScore for targetId: ${targetId}, ${JSON.stringify(error)}`);
    return 0;
  }
}

async function getAddionalTargetsScore(userId, startDate, endDate, noOfRequiredTargetsMet) {
  const targetStaticData = await targetService.getStaticTargetsByDuration(7);
  const targetIds = Object.keys(targetStaticData).filter(targetId => !CONSISTENCY_TARGETIDS.includes(Number(targetId)))
  const targetAchievementsData = await targetAchievedService.getTargetsAchievedByIsMet(userId, startDate, endDate, targetIds, true, false);
  const targetAchievementsPerTargetId = targetIds.reduce((acc, targetId) => ({ ...acc, [targetId]: 0 }), {});

  for (const targetAchievementDoc of targetAchievementsData) {
    const targetId = targetAchievementDoc.targetId;
    targetAchievementsPerTargetId[targetId]++;
  }
  const isAnyMetPerWeek =
    (Object.values(targetAchievementsPerTargetId)?.filter(
      (metCount) => metCount >= noOfRequiredTargetsMet
    )?.length > 0) ? true : false;

  return isAnyMetPerWeek ? 10 : 0;
}

async function getGoalScore(userId, UTCOffsetMin) {
  const goalStaticData = await targetService.getStaticTargetsByDuration(90);
  const endDate = getLocalDateTime(new Date().toISOString(), UTCOffsetMin).split("T")[0];
  const startDate = getAdjustedDate(endDate, -90);
  // Targets set by user & cehcking whether they are active or not
  const goalSetData = await targetService.getLatestTargetsByDate(userId, Object.keys(goalStaticData), endDate);
  const targetsData = {};
  for (const targetSet of goalSetData) {
    const targetId = Number(targetSet.targetId);
    if (!targetSet?.isActive || targetId == 19) {
      continue;
    }
    targetsData[targetId] = {
      name: goalStaticData[targetId].name,
      favorability: goalStaticData[targetId].favorability,
    };
  }

  const targetIds = Object.keys(targetsData);
  const targetAchievementsData = await targetAchievedService.getTargetsAchievedByDateRange(userId, startDate, endDate, targetIds, false );
  const targetAchievementsPerTargetId = targetIds.reduce((acc, targetId) => ({ ...acc, [targetId]: [] }), {});

  for (const targetAchievementDoc of targetAchievementsData) {
    const targetId = targetAchievementDoc.targetId;
    if (targetAchievementDoc.value && targetAchievementDoc.date)
      targetAchievementsPerTargetId[targetId].push({value: targetAchievementDoc.value, date: targetAchievementDoc.date });
  }
  let score = 0;
  for (const targetId in targetAchievementsPerTargetId) {
    score += getScoreForEachTargetId(targetAchievementsPerTargetId[targetId], targetsData[targetId]?.favorability, endDate);
  }

  return Math.min(score, GOAL_MAX_SCORE);
}

// Finding the longest no of weeks - streak from recent date
function getScoreForEachTargetId(values, favorability, endDate) {
  const latestValuesPerWeek = getLatestValuesPerWeek(values, endDate);
  let noOfWeeks = 0; // Streak, how many weeks user has made progress
  for (let i = 0; i < latestValuesPerWeek.length - 1; i++) {
    const weekKey = latestValuesPerWeek[i].weekKey;
    // Checking (Number(weekKey) == i + 1), as no week should be skipped
    if ((Number(weekKey) == i + 1) && favorability == "Positive" && latestValuesPerWeek[i].value >= latestValuesPerWeek[i + 1].value) {
      noOfWeeks++;
    } 
    else if ((Number(weekKey) == i + 1) && favorability == "Negative" && latestValuesPerWeek[i].value <= latestValuesPerWeek[i + 1].value) {
      noOfWeeks++;
    } 
    else {
      break;
    }
  }
  return noOfWeeks * 10;
}

/**
here, we are checking if user has made week on week progress
So, we are taking latest targetAchieved value per week and comparing with consecutive week values
Streak should be continuous, any skipped week number won't extend streak
For,  
values = [
  {
    value: 100,
    date: "2024-03-19",
  },
  {
    value: 97.5,
    date: "2024-03-18",
  },
  {
    value: 97.5,
    date: "2024-03-17",
  },
  {
    value: 86.5,
    date: "2024-03-16",
  },
  {
    value: 65,
    date: "2024-03-15",
  },
  {
    value: 74.5,
    date: "2024-03-14",
  },
  {
    value: 90,
    date: "2024-03-13",
  },
  {
    value: 73,
    date: "2024-03-12",
  },
  {
    value: 84.5,
    date: "2024-03-11",
  },
  {
    value: 92,
    date: "2024-03-10",
  },
  {
    value: 65,
    date: "2024-03-09",
  },
  {
    value: 99,
    date: "2024-02-29",
  },
  {
    value: 100,
    date: "2024-02-12",
  },
  {
    value: 100,
    date: "2024-02-11",
  },
  {
    value: 82,
    date: "2024-02-10",
  },
  {
    value: 89,
    date: "2024-02-09",
  },
  {
    value: 100,
    date: "2024-02-08",
  },
  {
    value: 97,
    date: "2024-02-07",
  },
  {
    value: 97,
    date: "2024-02-06",
  },
  {
    value: 100,
    date: "2024-02-05",
  },
  {
    value: 84,
    date: "2024-02-04",
  },
  {
    value: 78,
    date: "2024-02-03",
  },
  {
    value: 100,
    date: "2024-02-02",
  },
  {
    value: 85,
    date: "2024-02-01",
  },
  {
    value: 79,
    date: "2024-01-31",
  },
  {
    value: 79,
    date: "2024-01-30",
  },
  {
    value: 91,
    date: "2024-01-29",
  },
  {
    value: 98,
    date: "2024-01-28",
  },
  {
    value: 68.5,
    date: "2024-01-27",
  },
  {
    value: 98,
    date: "2024-01-26",
  },
  {
    value: 98,
    date: "2024-01-25",
  },
  {
    value: 54,
    date: "2023-12-26",
  },
  {
    value: 55,
    date: "2023-12-25",
  },
  {
    value: 53,
    date: "2023-12-24",
  },
  {
    value: 61,
    date: "2023-12-23",
  },
  {
    value: 64,
    date: "2023-12-22",
  },
]

groupedByWeek will be:
{
  '1': [
    { value: 100, date: '2024-03-19' },
    { value: 97.5, date: '2024-03-18' },
    { value: 97.5, date: '2024-03-17' },
    { value: 86.5, date: '2024-03-16' },
    { value: 65, date: '2024-03-15' }
  ],
  '2': [
    { value: 74.5, date: '2024-03-14' },
    { value: 90, date: '2024-03-13' },
    { value: 73, date: '2024-03-12' },
    { value: 84.5, date: '2024-03-11' },
    { value: 92, date: '2024-03-10' },
    { value: 65, date: '2024-03-09' }
  ],
  '4': [ { value: 99, date: '2024-02-29' } ],
  '6': [
    { value: 100, date: '2024-02-12' },
    { value: 100, date: '2024-02-11' },
    { value: 82, date: '2024-02-10' },
    { value: 89, date: '2024-02-09' }
  ],
  '7': [
    { value: 100, date: '2024-02-08' },
    { value: 97, date: '2024-02-07' },
    { value: 97, date: '2024-02-06' },
    { value: 100, date: '2024-02-05' },
    { value: 84, date: '2024-02-04' },
    { value: 78, date: '2024-02-03' },
    { value: 100, date: '2024-02-02' }
  ],
  '8': [
    { value: 85, date: '2024-02-01' },
    { value: 79, date: '2024-01-31' },
    { value: 79, date: '2024-01-30' },
    { value: 91, date: '2024-01-29' },
    { value: 98, date: '2024-01-28' },
    { value: 68.5, date: '2024-01-27' },
    { value: 98, date: '2024-01-26' }
  ],
  '9': [ { value: 98, date: '2024-01-25' } ],
  '13': [
    { value: 54, date: '2023-12-26' },
    { value: 55, date: '2023-12-25' },
    { value: 53, date: '2023-12-24' },
    { value: 61, date: '2023-12-23' },
    { value: 64, date: '2023-12-22' }
  ]
}

latestValuesPerWeek will be:
[
  { weekKey: '1', value: 100, date: '2024-03-19' },
  { weekKey: '2', value: 74.5, date: '2024-03-14' },
  { weekKey: '4', value: 99, date: '2024-02-29' },
  { weekKey: '6', value: 100, date: '2024-02-12' },
  { weekKey: '7', value: 100, date: '2024-02-08' },
  { weekKey: '8', value: 85, date: '2024-02-01' },
  { weekKey: '9', value: 98, date: '2024-01-25' },
  { weekKey: '13', value: 54, date: '2023-12-26' }
]
 */
function getLatestValuesPerWeek(values, endDate) {
  const groupedByWeek = values.reduce((acc, curr) => {
      const weekKey = Math.floor((getDuration(new Date(endDate).toISOString(), new Date(curr.date).toISOString())) / (60 * 60 * 24 * 7)) + 1;
      if (!acc[weekKey]) {
          acc[weekKey] = [];
      }
      acc[weekKey].push(curr);
      return acc;
  }, {});
  const latestValuesPerWeek = [];
  for (const weekKey in groupedByWeek) {
      if (Object.hasOwnProperty.call(groupedByWeek, weekKey)) {
          const weekValues = groupedByWeek[weekKey];
          const latestValue = weekValues.reduce((latest, current) => {
              return (new Date(current.date) > new Date(latest.date)) ? current : latest;
          });
          latestValuesPerWeek.push({weekKey, ...latestValue});
      }
  }

  return latestValuesPerWeek;
}

function getWellnessIds(details) {
  const constructedIds = details.reduce((acc, detail) => {
    if (detail.details && detail.details.length > 0) {
      detail.details.forEach((subDetail) => {
        if (subDetail.id !== undefined && subDetail.id !== null) {
          const { targetId } = subDetail;
          acc.push({ id: detail.id + "." + subDetail.id, targetId });
        }
      });
    } else {
      const { targetId } = detail;
      acc.push({ id: detail.id + ".0", targetId });
    }
    return acc;
  }, []);
  return constructedIds;
}

function getGifByScore(score = 0) {
  if (!score || score < 50) {
    return scoreGifs?.red || null;
  } else if (score < 75) {
    return scoreGifs?.blue || null;
  } else {
    return scoreGifs?.green || null;
  }
}

function getWellnessScoreDoc(userId, score) {
  const doc = {
    userId,
    score: Number(score),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  return doc;
}

module.exports = {
  getWellnessScore,
  getWellnessScoreWithDetails,
  getWellnessScoreDoc,
  MAX_TOTAL_SCORE,
}

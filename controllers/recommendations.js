const { asyncHandler, deepCopy, getRequestedLanguage, getLanguageText, getFileExtension, getFileType, sortByDateKeyDesc } = require("../utils/helpers");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const targetService = require("../service/targets");
const { devicesService } = require("../service/devices");
const { trackersService } = require("../service/trackers");
const { getUTCOffsetValue } = require("../service/userProfile");
const { getStaticTargetsMap }  = require("../utils/staticData")
const recommendationsStaticData = require("../service/recommendations.json");
const wellnessStaticData = require("../service/wellness.json");

const { getAssignedForms } = require("../utils/forms");
const targetCronService = require("../service/targetCron");
const { generateDynamicShortUrl } = require("../utils/dynalinks");
const { getWellnessScoreWithDetails, MAX_TOTAL_SCORE, getWellnessScoreDoc } = require("./wellnessScore");
const { getUserRecommendations, upsertUserRecommendations } = require("../service/recommendations");
const { upsertWellnessScore } = require("../service/wellness_score");

const PROFILE_FORM_TEMPLATEID = 1;
const recommendationStaticData = config.recommendations;
const recommendationDocType = 'system';

const themeStyleMap = require("../service/recommendations-style-map.json");

module.exports.getRecommendations = asyncHandler(async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded?.user_guid;
    const reqLanguage = getRequestedLanguage(req);
    const staticResponseData = deepCopy(recommendationsStaticData);
    let { type: recommendationType, data: recommendationData} = await getRecommendation(userId);

    let totalScore = 0;
    if(recommendationType == 'learn_more') { // No recommendations, then only show wellness score as mediaOverlayText
      const UTCOffsetMin = await getUTCOffsetValue(userId);
      const activeWindowData = await targetCronService.getData(userId);
      const { activeWindowStartDate, activeWindowEndDate } = activeWindowData || {};
      if(activeWindowStartDate && activeWindowEndDate) {
        const wellnessScoreDetails = await getWellnessScoreWithDetails(userId, activeWindowStartDate, activeWindowEndDate, UTCOffsetMin, wellnessStaticData.details);
        totalScore = Math.min(wellnessScoreDetails.reduce((acc, detail) => acc + detail?.score || 0, 0), MAX_TOTAL_SCORE);
        const wellnessScoreDoc = getWellnessScoreDoc(userId, totalScore);
        await upsertWellnessScore(userId, wellnessScoreDoc);
      }
    }

    const systemRecommendationResponse = await getRecommendationResponse(reqLanguage, staticResponseData, recommendationType, recommendationData, totalScore);
    const status = recommendationType == 'learn_more' ? 'closed' : 'open';
    recommendationType = recommendationType == 'learn_more' ? null : recommendationType;
    const doc = getRecommendationDoc(userId, recommendationDocType, recommendationType, status, systemRecommendationResponse.content);
    await upsertUserRecommendations(userId, doc, recommendationDocType);

  let allRecommendations = await getUserRecommendations(userId, "open", ["system", "score"]);
  allRecommendations = sortByDateKeyDesc(allRecommendations, 'lastSentAt'); // Latest sent notifcation nudges should be at top
  const formattedRecommendations = allRecommendations.map((doc) => {
    return formatRecommendationResponse(doc);
  })
  return res.status(200).json({ success: true, message: `Recommendations for userId: ${userId}`, data: [systemRecommendationResponse, ...formattedRecommendations] });
  } catch (error) {
    logger.error(`Error in getRecommendations: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
});

async function getRecommendation(userId) {
  // Run profile form check and target data fetch in parallel
  const [assignedForms, targetMap] = await Promise.all([
    getAssignedForms(userId, PROFILE_FORM_TEMPLATEID),
    getStaticTargetsMap()
  ]);

  const latestProfileForm = assignedForms?.[0];
  if(!latestProfileForm?.isComplete) {
    const type = 'form';
    return { type, data: { formId: latestProfileForm?.id} };
  }

  // Get All the targets which are set as active
  const allTargetIds = Object.keys(targetMap).filter(key => targetMap[key].isDefault).map(Number);
  
  // Run target and tracker operations in parallel
  const [targetData, respectiveTrackerIds] = await Promise.all([
    targetService.getLatestTargetsByIds(userId, allTargetIds),
    trackersService.getTrackersIdsByTargetIds(allTargetIds)
  ]);

  const allTargetsEnabled = targetData.filter(doc => doc.isActive).map(doc => Number(doc.targetId));
  
  // Check if any device is connected which can measure these trackerIds
  const { isEnable: isDeviceConnected, type, trackerIds, deviceId } = await devicesService.isEnableTrackerForUser(userId, respectiveTrackerIds);
  if(!isDeviceConnected) {
    return { type, data: { trackerIds, deviceId } };
  } 
  return { type: 'learn_more', data: {} };
}

async function getRecommendationResponse(reqLanguage, staticJson, type, recommendationData, wellnessScore) {
  // Fetch tracker and device details in parallel if needed
  let trackerDetails = [], deviceDetails = null;
  
  if (recommendationData?.trackerIds?.length) {
    // Bulk fetch all tracker details at once
    trackerDetails = await Promise.all(
      recommendationData.trackerIds.map(trackerId => 
        trackersService.getTrackerDetailsById(Number(trackerId))
      )
    );
    trackerDetails = trackerDetails.filter(Boolean); // Remove any null results
  }

  if (recommendationData?.deviceId) {
    deviceDetails = await devicesService.getDeviceDetailsByDeviceId(Number(recommendationData.deviceId));
  }

  const trackerNames = trackerDetails?.map((tracker) => tracker.name).join(', ');
  const appendData = { 
    trackerName: trackerNames || null, 
    deviceName: deviceDetails?.name || null 
  };

  staticJson.content.title = getLanguageText(`recommendation_title_${type}`, reqLanguage);
  staticJson.content.subtitle = getLanguageText(`recommendation_subtitle_${type}`, reqLanguage, appendData);
  
  // System recommendation has "high" priority, so keeping it as it is i.e. red dark/light, same goes for style object
  staticJson.content.media.imageUrl = recommendationStaticData[type].imageUrl;
  staticJson.content.media.type = getFileType(getFileExtension(staticJson.content.media.imageUrl.light));

  const title = getLanguageText(`recommendation_primary_action_title_${type}`, reqLanguage);
  const url = await getPrimaryActionUrl(type, recommendationData);
  staticJson.content.primaryAction = { title, url };

  if (type == 'learn_more') {
    // No recommendations, then only show wellness score as mediaOverlayText
    staticJson.content.media.mediaOverlayText = wellnessScore.toString();
  } else {
    delete staticJson.content.media.mediaOverlayText;
  }
  return staticJson;
}

async function getPrimaryActionUrl(type, data) {
  switch(type) {
    case 'form': {
      if(!data?.formId) return null;
      const formUrl = `${config.dynalinks.forms}?formId=${data.formId}`;
      const url = await generateDynamicShortUrl(formUrl, 'Form')
      return url;
    }
    case 'device': {
      if(!data?.trackerIds) return null;
      const deviceUrl = `${config.dynalinks.device_connection}?trackerIds=${data.trackerIds}&isConnected=false`;
      const url = await generateDynamicShortUrl(deviceUrl, 'Unconnected Devices');
      return url;
    }
    case 'permission': {
      if(!data?.deviceId) return null;
      const deviceUrl = `${config.dynalinks.device_permission}?deviceId=${data.deviceId}&isConnected=true`;
      const url = await generateDynamicShortUrl(deviceUrl, 'Devices - Trackers Permissions');
      return url;
    }
    case 'learn_more': {
      const deviceUrl = config.dynalinks.wellness_score;
      const url = await generateDynamicShortUrl(deviceUrl, 'Wellness-Score');
      return url;
    }
  }
  return null;
}

function getRecommendationDoc(userId, type, category, status, content) {
  const currentTime = new Date().toISOString();
  const ttlDate = new Date(new Date().getTime() + 8 * 24 * 60 * 60 * 1000);
  const doc = {
    userId,
    type,
    action_url: content?.media?.primaryAction?.url || null,
    content: {
      title: content?.title || null,
      message: content?.subtitle || null,
    },
    timestamp: currentTime,
    category, // device, permission, form, etc
    priority: "high", // Always high for type == system
    tool_origin: null,
    ttl: ttlDate.toISOString(),
    status,
    createdAt: currentTime,
    updatedAt: currentTime
  };
  return doc;
}

function formatRecommendationResponse(docFromDB) {
  const doc = deepCopy(recommendationsStaticData);
  doc.style.card = themeStyleMap[docFromDB.priority];
  doc.content.title = docFromDB?.content?.title || '';
  doc.content.subtitle = docFromDB?.content?.message || '';
  doc.content.media = recommendationsStaticData.content.media;
  doc.content.primaryAction = recommendationsStaticData.content.primaryAction;
  delete doc.content.media.mediaOverlayText;
  return doc;
}

module.exports.getPrimaryActionUrl = getPrimaryActionUrl;

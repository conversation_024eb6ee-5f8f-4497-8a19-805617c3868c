const { triggerService } = require("../service/triggers");
const logsService = require("../service/logs");
const { devicesService } = require("../service/devices");
const { config } = require("../environment/index");
const { asyncHandler, getDayTimeRange } = require("../utils/helpers");
const { trackersService } = require("../service/trackers");
const { getUTCOffsetValue } = require("../service/userProfile");
const exerciseLogsService = require("../service/exerciseLogs");
const { log: logger } = require("../utils/logger");
const { analyzeGlucoseResponse, calculateCGMDetails } = require("../utils/cgmScoring");

const Ajv = require("ajv");
const addFormats = require("ajv-formats");

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);
const schema = require("../models/triggers.json");
const validate = ajv.compile(schema);

const mealLogTrackerId = 1, cgmTrackerId = 8;

module.exports.createTriggerLog = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    let { triggeredAt, mealLogId, mealTags } = req.body;

    if (!userId) {
      return res.status(400).json({ success: false, message: "userId missing in request", });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    triggeredAt = new Date(triggeredAt).toISOString();
    if (triggeredAt == "Invalid Date") {
      return next({ message: `Invalid triggeredAt date`, statusCode: 400, });
    }

    let expiresAt = new Date(triggeredAt);
    expiresAt.setHours(expiresAt.getHours() + 2);

    triggeredAt = new Date(triggeredAt).toISOString();
    const triggers = [{ triggeredAt, mealLogId, mealTags }];

    const newDoc = {
      userId,
      triggers,
      startedAt: triggeredAt,
      expiresAt: new Date(expiresAt).toISOString(),
    };

    const valid = validate(newDoc);

    if (!valid) {
      return next({
        message: `Validation failed: ${JSON.stringify(validate.errors)}`,
        statusCode: 400,
      });
    }

    const insertedId = await triggerService.createTrigger(newDoc);
    if (!insertedId) {
      return next({ message: "Failed to save triggers log", statusCode: 500 });
    }

    return res.status(200).json({
      success: true,
      message: `Trigger Log entry saved`,
      data: { insertedId },
    });
  } catch (error) {
    logger.error(`Triggers | createTriggerLog error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to create trigger log", statusCode: 500 });
  }
});

module.exports.getAllTriggers = asyncHandler(async (req, res, next) => {
  try {
    const userId = '7ad86571-5d9a-4a7b-ace0-fbf6946fd540';// req.query?.user_guid || req.decoded.user_guid;
    const tag = req.query?.tag;

    if (!userId) {
      return next({ message: "userId or user_guid is missing in request", statusCode: 400 });
    }

    let startTime = new Date(), endTime = new Date();
    if (req.query?.startTime && req.query?.endTime) {
      try {
        startTime = new Date(req.query?.startTime).toISOString();
        endTime = new Date(req.query?.endTime).toISOString();
      } catch (e) {
        return next({ message: "Invalid startTime or endTime", statusCode: 400 });
      }
    }
    else {
      const UTCOffsetMin = await getUTCOffsetValue(userId);
      const timeRange = getDayTimeRange(UTCOffsetMin);
      startTime = timeRange.startTime;
      endTime = timeRange.endTime;
    }

    let triggerLogs, message;

    if (tag) {
      if (!tag?.trim()) {
        return next({ message: "Invalid tag", statusCode: 400 });
      }
      triggerLogs = await triggerService.getTriggersByTag(userId, startTime, endTime, tag);
      message = `All trigger logs having "${tag}" tag for user: ${userId} for duration: ${Math.ceil((new Date(endTime) - new Date(startTime)) / (1000 * 60 * 60 * 24))}`;
    } else {
      triggerLogs = await triggerService.getAllTriggers(userId, startTime, endTime);
      message = `All trigger logs for user: ${userId} for duration: ${Math.ceil((new Date(endTime) - new Date(startTime)) / (1000 * 60 * 60 * 24))}`;
    }

    const data = [];
    const mealLogImageUrl = (await trackersService.getTrackerDetailsById(mealLogTrackerId))?.details?.media?.enabledImageUrl || "https://example.com/default_icon.png";

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);

    // Process each trigger log
    for (const triggerLog of triggerLogs) {
      const { startedAt, expiresAt, _id, triggers, metadata } = triggerLog;

      // Get CGM data for this trigger window
      const cgmData = await logsService.getAllLogsByDateRange(config.INDEX.egvs, userId, startedAt, expiresAt);

      // Calculate score and CGM details
      const mealStartTime = new Date(startedAt);
      const score = analyzeGlucoseResponse(cgmData, mealStartTime);
      const cgmDetails = calculateCGMDetails(cgmData);

      // Format triggers with graphIconURL
      const formattedTriggers = triggers?.map(trigger => ({
        ...trigger,
        graphIconURL: mealLogImageUrl
      })) || [];

      // Format metadata if exists
      let formattedMetadata = [];
      if (metadata && metadata.length > 0) {
        formattedMetadata = metadata.map(meta => ({
          ...meta,
          graphIconURL: mealLogImageUrl
        }));
      }

      // Add activities if CGM device is connected
      if(isDeviceConnected) {
        const activities = await exerciseLogsService.getLogsByDateRange(userId, startedAt, expiresAt, true);
        const activityImageUrl = (await trackersService.getTrackerDetailsById(4))?.details?.media?.enabledImageUrl || mealLogImageUrl;

        activities.forEach(({ id, exerciseName, timestamp, ...activityDetails }) => {
          formattedMetadata.push({
            eventId: id,
            eventType: exerciseName,
            timestamp,
            imageUrl: activityImageUrl,
            details: activityDetails,
            graphIconURL: mealLogImageUrl
          });
        });
      }

      const triggerBucket = {
        startedAt,
        triggers: formattedTriggers,
        details: {
          ...cgmDetails,
          score
        },
        expiresAt,
        _id
      };

      // Add metadata if it exists
      if (formattedMetadata.length > 0) {
        triggerBucket.metadata = formattedMetadata;
      }

      data.push(triggerBucket);
    }

    return res.status(200).json({ success: true, message, data });
  } catch (error) {
    logger.error(`Triggers | getAllTriggers error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get all triggers", statusCode: 500 });
  }
});

module.exports.getTriggerById = asyncHandler(async (req, res, next) => {
  try {
    const userId = '7ad86571-5d9a-4a7b-ace0-fbf6946fd540';//req.query?.user_guid || req.decoded.user_guid;
    const triggerId = req.params.triggerId;

    if (!userId || !triggerId) {
      return res.status(400).json({
        success: false,
        message: "userId or triggerId missing in request",
      });
    }

    const triggerLog = await triggerService.getTriggerById(userId, triggerId);
    if (!triggerLog) {
      return next({ message: "Invalid triggerId or you don't have permission", statusCode: 403 });
    }

    const { startedAt, expiresAt, _id, triggers, metadata } = triggerLog;
    const mealLogImageUrl = (await trackersService.getTrackerDetailsById(mealLogTrackerId))?.details?.media?.enabledImageUrl || "https://example.com/default_icon.png";

    // Get CGM data for this trigger window
    const cgmData = await logsService.getAllLogsByDateRange(config.INDEX.egvs, userId, startedAt, expiresAt);

    // Calculate score and CGM details
    const mealStartTime = new Date(startedAt);
    const score = analyzeGlucoseResponse(cgmData, mealStartTime);
    const cgmDetails = calculateCGMDetails(cgmData);

    // Format triggers with graphIconURL
    const formattedTriggers = triggers?.map(trigger => ({
      ...trigger,
      graphIconURL: mealLogImageUrl
    })) || [];

    // Format metadata if exists
    let formattedMetadata = [];
    if (metadata && metadata.length > 0) {
      formattedMetadata = metadata.map(meta => ({
        ...meta,
        graphIconURL: mealLogImageUrl
      }));
    }

    // Add activities for this trigger window
    const activities = await exerciseLogsService.getLogsByDateRange(userId, startedAt, expiresAt, true);
    const activityImageUrl = (await trackersService.getTrackerDetailsById(4))?.details?.media?.enabledImageUrl || mealLogImageUrl;

    activities.forEach(({ id, exerciseName, timestamp, ...activityDetails }) => {
      formattedMetadata.push({
        eventId: id,
        eventType: exerciseName,
        timestamp,
        imageUrl: activityImageUrl,
        details: activityDetails,
        graphIconURL: mealLogImageUrl
      });
    });

    const data = {
      startedAt,
      triggers: formattedTriggers,
      details: {
        ...cgmDetails,
        score
      },
      expiresAt,
      _id
    };

    // Add metadata if it exists
    if (formattedMetadata.length > 0) {
      data.metadata = formattedMetadata;
    }

    return res.status(200).json({
      success: true,
      message: `Found trigger log id: ${triggerId}`,
      data,
    });
  } catch (error) {
    logger.error(`Triggers | getTriggerById error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get trigger by ID", statusCode: 500 });
  }
});

module.exports.updateTrigger = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const { triggerId } = req.params;
    const { triggeredAt, mealLogId, mealTags } = req.body;

    if (!userId) {
      return res.status(400).json({success: false, message: "userId missing in request" });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    if (new Date(triggeredAt) === "Invalid Date") {
      return res.status(400).json({success: false, message: "Invalid triggeredAt date-time" });
    }

    if (!triggerId) {
      return res.status(400).json({success: false, message: "triggerId is required" });
    }

    const triggerLog = await triggerService.getTriggerById(userId, triggerId);

    if (triggerLog) {
      const { triggers: existingTriggers, startedAt, expiresAt } = triggerLog;
      const triggeredAt = new Date(req.body.triggeredAt);
      console.log(triggeredAt, startedAt, expiresAt);
      if (triggeredAt >= new Date(startedAt) && triggeredAt <= new Date(expiresAt)) {
        let newExpiresAt = triggerLog.expiresAt;
        const existingMealLog = existingTriggers.find(trigger => trigger.mealLogId == mealLogId);
        // expiresAt will be updated only when new MealLog entry is added, not when existing MealLog is updated
        if(!existingMealLog){
          newExpiresAt = new Date(triggeredAt);
          newExpiresAt.setHours(newExpiresAt.getHours() + 2);
          newExpiresAt = newExpiresAt.toISOString();
        }
        const triggersWithoutGivenMealLogId = existingTriggers.filter(trigger => trigger.mealLogId !== mealLogId); // Deleting older mealLogId object if present
        const updatedTriggers = [
          ...triggersWithoutGivenMealLogId,
          { mealLogId, triggeredAt: triggeredAt.toISOString(), mealTags },
        ];
        await triggerService.updateTrigger(triggerId, {
          triggers: updatedTriggers,
          expiresAt: newExpiresAt,
        });
        return res.status(200).json({success: true, message: `Updated trigger log: ${triggerId} for user: ${userId}` });
      } else {
        return next({ message: `Invalid triggered At`, statusCode: 400 });
      }
    } else {
      return next({ message: `No trigger log found for user: ${userId}`, statusCode: 400, });
    }
  } catch (error) {
    logger.error(`Triggers | updateTrigger error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to update trigger", statusCode: 500 });
  }
});

module.exports.upsertTrigger = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const { triggeredAt } = req.body;

    if (!userId) {
      return res.status(400).json({success: false, message: "userId missing in request" });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    if (new Date(triggeredAt) === "Invalid Date") {
      return res.status(400).json({success: false, message: "Invalid triggeredAt date-time" });
    }

    // Check if any Active Trigger exists, otherwise create Trigger
    const triggerLog = await triggerService.getActiveTrigger(userId);
    const triggerId = triggerLog?._id || null;

    if (triggerId) {
      req.params.triggerId = triggerId;
      return this.updateTrigger(req, res, next);
    } else {
      return this.createTriggerLog(req, res, next);
    }
  } catch (error) {
    logger.error(`Triggers | upsertTrigger error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to upsert trigger", statusCode: 500 });
  }
});

module.exports.createEvent = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    let triggerId = req.params.triggerId;
    const eventsData = req.body;

    if (!userId) {
      return res.status(400).json({ success: false, message: "userId or triggerId missing in request" });
    }

    // Check if any device is connected which can measure CGM
    const { isEnable: isDeviceConnected } = await devicesService.isEnableTrackerForUser(userId, [cgmTrackerId]);
    if(!isDeviceConnected) {
      return next({ message: `No CGM measuring device connected`, statusCode: 400, });
    }

    let triggerLog;
    if(triggerId) {
      triggerLog = await triggerService.getTriggerById(userId, triggerId);
    }
    else {
      triggerLog = await triggerService.getActiveTrigger(userId);
      triggerId = triggerLog?._id || null;
    }

    for (let i = 0; i < eventsData.length; i++) {
      const { eventId, eventType, timestamp } = eventsData[i];
      if (!eventId || !eventType || !timestamp || new Date(timestamp) == "Invalid Date") {
        return next({ message: "Invalid eventId or eventType or timestamp", statusCode: 400 });
      }
    }

    let metadata;
    if (triggerLog) {
      if (!triggerLog.metadata) {
        metadata = eventsData;
      } else {
        metadata = [ ...triggerLog.metadata, ...eventsData ];
      }

      await triggerService.updateTrigger(triggerId, { metadata });
      return res.status(200).json({success: true, message: `Updated trigger log: ${triggerId} for user: ${userId}`});
    } else {
      return next({ message: `No trigger log found with ${triggerId} for user: ${userId}`, statusCode: 400, });
    }
  } catch (error) {
    logger.error(`Triggers | createEvent error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to create event", statusCode: 500 });
  }
});

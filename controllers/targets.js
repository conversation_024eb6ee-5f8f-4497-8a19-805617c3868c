const targetService = require("../service/targets");
const targetAchievedService = require("../service/targets_achieved");
const { trackersService } = require("../service/trackers");
const { devicesService } = require("../service/devices");
const targetCronService = require("../service/targetCron");
const { roundOffNumber, parsePaginationParams, validateData, getLocalDateString } = require("../utils/helpers");
const { getStaticTargetsMap } = require("../utils/staticData");
const { log: logger } = require("../utils/logger");
const DEFAULT_SESSION_VALUE = 5;

const notificationService = require("../service/push-notifications");
const notificationEventNames = notificationService.notificationEventNames;
const { createRequestEntry } = require("../utils/requests");

// Target Defnition
module.exports.setTargetById = async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const doctorId = req.query?.doctor_guid;
    const source = req.query?.source;
    // If doctor_guid is present in query param then user_guid must be there in query param
    if(doctorId && !req.query?.user_guid){
      return next({ message: `user_guid missing in request query param`, statusCode: 400 });
    }

    const targetId = req.body?.targetId;

    if (!userId) {
      return next({ message: "user_id missing in request", statusCode: 400 });
    }
    if (!targetId) {
      return next({ message: "targetId missing in request query", statusCode: 400 });
    }
    if (source && source != 'gpt') {
      return next({ message: "Invalid source in request query", statusCode: 400 });
    }

    const isGPT = source == 'gpt';
    // Validate target body based on target Id
    const targetsMap = await getStaticTargetsMap();
    const { valid, errors } = validateData(req.body, targetsMap[targetId].validate);
    if (!valid) {
      return next({ message: 'Validation Failed', error: errors, statusCode: 400 });
    }

    // Add metadata and insert in DB
    const createdAt = new Date().toISOString();
    const body = {
      userId,
      createdAt,
      ...req.body,
      isActive: req.body?.isActive || false,
      session: Number(req.body?.session || DEFAULT_SESSION_VALUE),
      threshold: Number(req.body?.threshold || targetsMap[targetId].threshold || 0),
    };
    
    if(!isGPT) {
      const { isEnable: enableTargetForUser } = await devicesService.isEnableTargetForUser(userId, targetId);
      if (!enableTargetForUser) {
        return res.status(200).json({ success: false, message: "Unable to set target. Please associate with at least one device related to the tracker, and ensure the tracker is enabled to read data from the source"});
      }
    }

    const targetDetails = await targetService.insertNewTarget(body);

    if(targetDetails && (doctorId || req.query?.user_guid)) {
      const senderUserId = doctorId || req.decoded.user_guid; // doctor or buser
      const receiverUserId = req.query?.user_guid; // ~ user_guid
      const isNotificationSent = await notificationService.sendNotificationToUser(senderUserId, receiverUserId, notificationEventNames.newTargetAssigned, { target_inserted_id: targetDetails });
      if(isNotificationSent) {
        const accessToken = req.headers['x-access-token'];
        await createRequestEntry(receiverUserId, accessToken, notificationEventNames.newTargetAssigned, { entity_id: targetDetails });
      }
    }

    if (targetDetails) {
      return res.status(200).json({
        success: true,
        data: { insertedId: targetDetails },
      });
    } else {
      return res.status(200).json({
        success: false,
        message: `Failed to update target`,
      });
    }
  } catch (err) {
    logger.error(err);
    return next({ message: err.message, statusCode: 500 });
  }
};

module.exports.getTargetById = async function (req, res, next) {
  try {
    const { user_guid: userId } = req.decoded;
    const targetId = req.params?.targetId;

    if (!userId) {
      return next({ message: "user_id missing in request", statusCode: 400 });
    }
    if (!targetId) {
      return next({ message: "targetId missing in request query", statusCode: 400 });
    }
    const targetDetails = await targetService.getLatestTargetById(userId, targetId);

    if (targetDetails) {
      res.status(200).json({
        success: true,
        data: { targetDetails },
      });
    } else {
      res.status(200).json({
        success: false,
        message: `User has no targets with provided target Id`,
      });
    }
  } catch (error) {
    logger.error(`getTargetById error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
};

module.exports.getAllTargets = async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded?.user_guid;
    const targetsMap = await getStaticTargetsMap();
    for (const key in targetsMap) {
      delete targetsMap[key].validate;
    }

    if (!userId) {
      return res.status(200).json(targetsMap);
    }
    const targetIds = Object.keys(targetsMap)

    let data = await Promise.all(targetIds.map(targetId =>
      targetService.getLatestTargetById(userId, targetId)
    ))
    data = data.filter(x => x).map(x => {
      return {targetId: x.targetId, ...targetsMap[x.targetId], ...x}
    })

    if (data && data.length) {
      return res.status(200).json({ success: true, data });
    } else {
      return res.status(200).json({
        success: false,
        message: `User has no targets`,
      });
    }
  } catch (error) {
    logger.error(`getAllTargets error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
};


// Target Achieved

// Get targets achived by id
module.exports.getTargetAchievedById = async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const targetId = Number(req.params?.targetId || 0);

    if (!userId) {
      return next({ message: "user_id missing in request", statusCode: 400 });
    }
    if (!targetId) {
      return next({ message: "targetId missing in request query", statusCode: 400 });
    }
    const targetsMap = await getStaticTargetsMap();
    let targetData = await targetService.getLatestTargetById(userId, targetId);
    if(!targetData || !targetData?.isActive){
      return res.status(200).json({ success: true, message: "Target is inactive", data: {targetDetails: {}} });
    }
    let UTCOffSetMin = 0;
    if (req.headers?.utcoffsetmin !== undefined && req.headers?.utcoffsetmin !== null) {
      UTCOffSetMin = Number(req.headers.utcoffsetmin);
      logger.info(`getTargetAchievedById UTCOffSetMin: ${UTCOffSetMin}`)
    }
    else {
      return next({ message: "utcoffsetmin missing in request", statusCode: 400 });
    }

    const pagination = req.query?.pagination == 'true' ? true : false;

    let data = {targetDetails: { details: {}, dayWiseData: []}}, isDateRange = false, startDate, endDate;
    if (req.query?.startDate && req.query?.endDate) {
      startDate = getLocalDateString(req.query?.startDate, UTCOffSetMin);
      endDate = getLocalDateString(req.query?.endDate, UTCOffSetMin);
      data = { ...data, startDate, endDate };
      isDateRange = true;
    }
    else if(req.query?.startDate || req.query?.endDate){
      return next({ message: "Date range missing in request", statusCode: 400 });
    }
    else{
      const duration = Number(targetsMap[targetId].duration);
      if(duration == 7 || duration == -1){
        const targetCronData = await targetCronService.getData(userId);
        if(!targetCronData){
          return res.status(200).json({success: true, message: `The user has no target achievement`, data: {targetDetails: {}} });
        }
        startDate = targetCronData.activeWindowStartDate;
        endDate = targetCronData.activeWindowEndDate; 
        data = { ...data, startDate, endDate };
      }
      else{
        const currentDate = new Date();
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - duration - 1);
        startDate = getLocalDateString(startDate.toISOString(), UTCOffSetMin);
        endDate = getLocalDateString(currentDate.toISOString(), UTCOffSetMin);
        data = { ...data, startDate, endDate };      
      }
    }

    if(!targetsMap[targetId]){
      return next({success: false, message: `Invalid req: Invalid target Id`, statusCode: 400 });
    }

    if(pagination) {
      var { limit, page } = parsePaginationParams(req.query);
      from = limit * (page - 1)
      data.targetDetails.dayWiseData = await targetAchievedService.getTargetAchievedByIdAndPagination(userId, from, limit, targetId);
    }
    else{
      data.targetDetails.dayWiseData = await targetAchievedService.getTargetAchievedByIdAndDateRange(userId, targetId, startDate, endDate);
    }

    const { trackerIds, favorability, name: targetName, unit, duration } = targetsMap[targetId];
    const trackerData = await trackersService.getTrackerDetailsById(trackerIds[0]);
    const imageUrl = trackerData?.details?.media?.enabledImageUrl || null;

    data.targetDetails.details = { userId, targetId: Number(targetId), targetName, unit, favorability, duration, trackerId: trackerIds[0], imageUrl, session: targetData?.session || DEFAULT_SESSION_VALUE, isMet: false };
    data.targetDetails.dayWiseData = data.targetDetails.dayWiseData.map(({ userId, targetId, ...required }) => required);

    // Updating isMet in details according to session value
    const { session } = data.targetDetails.details;
    const isMetCount = data.targetDetails.dayWiseData.reduce((count, entry) => (entry.isMet ? count + 1 : count), 0);
    if (isMetCount >= session) {
      data.targetDetails.details.isMet = true;
    }

    if(!pagination && data.targetDetails.details.duration == 7){
      // Filling default data if target achieved is not present for a date, only if target duration is 7
      const currentDate = Math.min(new Date(), new Date(endDate));
      const targetsByDateRange = await targetService.getTargetsByDateRange(userId, startDate, endDate, [targetId]);
      const targetIdsTargetValuesMapping = {};
      /**
       * e.g. targetIdTargetsMapping => { "targetId" : {"date": "targetValue"} }
          {
            "1": {
              "2023-01-01": 500,
              "2023-01-02": 600,
              "2023-01-03": 700,
              "2023-01-04": 500,
              "2023-01-05": 600,
            }
          }
      */
      for(let i = 0; i < targetsByDateRange.length; i++){
        const targetId = targetsByDateRange[i].targetId;
        const date = targetsByDateRange[i].createdAt.split('T')[0];
        if(!targetIdsTargetValuesMapping[targetId]) targetIdsTargetValuesMapping[targetId] = {};
        if(!targetIdsTargetValuesMapping[targetId][date]) targetIdsTargetValuesMapping[targetId][date] = targetsByDateRange[i].value;
      }
      const dateCursor = new Date(startDate);
      const targetsOnStartDate = await targetService.getLatestTargetsByDate(userId, [targetId], startDate);
      while (dateCursor <= currentDate) {
        const formattedDate = dateCursor.toISOString().split('T')[0];
        let latestTargetTillDate = 0;
        if(formattedDate == startDate){
          const targetData = targetsOnStartDate.find(doc => Number(doc.targetId) == targetId) || null;
          latestTargetTillDate = targetData?.value || 0;
          if(!targetIdsTargetValuesMapping[targetId]) targetIdsTargetValuesMapping[targetId] = {};
          targetIdsTargetValuesMapping[targetId][formattedDate] = latestTargetTillDate;
        }
        else {
          const targetIdMapping = targetIdsTargetValuesMapping[targetId];
          for (const date of Object.keys(targetIdMapping)) {
            if (new Date(date).getTime() <= new Date(formattedDate).getTime()) {
              latestTargetTillDate = targetIdMapping[date];
              break;
            }
          }        
        }
        const existingEntry = data.targetDetails.dayWiseData.find(entry => entry.date === formattedDate);
        if (!existingEntry) {
          const entry = { id: null, date: formattedDate, value: 0, target: 0, percentage: 0, isMet: false }; 
          entry.target = latestTargetTillDate;
          data.targetDetails.dayWiseData.push(entry);
        }
        dateCursor.setDate(dateCursor.getDate() + 1);
      }
      data.targetDetails.dayWiseData.sort((a, b) => a.date.localeCompare(b.date));
    }

    if(data.targetDetails.dayWiseData?.length && !pagination){
      return res.status(200).json({ success: true, message: `User's target achievements`, data, });    
    }
    else if (pagination) {
      return res.status(200).json({ success: true, message: `User's target achievements`, data: {targetDetails: data.targetDetails },});    
    }
    return res.status(200).json({ success: true, message: 'The user has no target achievement', data, });
  } catch (error) {
    logger.error(`getTargetAchievedById error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  };
};

// Get all targets achieved
module.exports.getAllTargetsAchieved = async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    if (!userId) {
      return next({ message: "user_id missing in request", statusCode: 400 });
    }
    const duration = Number(req.query?.duration || 7); // 7 if duration is not provided
    if(![7, 90].includes(duration)){
      return next({ message: "Invalid duration", statusCode: 400 });
    }
    let UTCOffSetMin = 0;
    if (req.headers?.utcoffsetmin !== undefined && req.headers?.utcoffsetmin !== null) {
      UTCOffSetMin = Number(req.headers.utcoffsetmin);
      logger.info(`getAllTargetsAchieved UTCOffSetMin: ${UTCOffSetMin}`)
    }
    else {
      return next({ message: "utcoffsetmin missing in request", statusCode: 400 });
    }

    const targetsMap = await getStaticTargetsMap();

    const pagination = req.query?.pagination == 'true' ? true : false;

    let startDate, endDate, isDateRange = false;
    let data = {targetDetails: {}};
    if (req.query?.startDate && req.query?.endDate) {
      startDate = getLocalDateString(req.query?.startDate, UTCOffSetMin);
      endDate = getLocalDateString(req.query?.endDate, UTCOffSetMin);
      isDateRange = true;
    }
    else if(req.query?.startDate || req.query?.endDate){
      return next({ message: "Date range missing in request", statusCode: 400 });
    }
    else{ // date range is not given
      if(duration == 7 || duration == -1){
        const targetCronData = await targetCronService.getData(userId);
        if(!targetCronData){
          return res.status(200).json({success: true, message: `The user has no target achievement`, data });
        }
        startDate = targetCronData.activeWindowStartDate;
        endDate = targetCronData.activeWindowEndDate; 
        data = { ...data, startDate, endDate };
      }
      else{
        const currentDate = new Date();
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - duration + 1);
        startDate = getLocalDateString(startDate.toISOString(), UTCOffSetMin);
        endDate = getLocalDateString(currentDate.toISOString(), UTCOffSetMin);

        data = { ...data, startDate, endDate };      
      }
    }
    const excludeTargetIds = [41, 42, 43, 44]; // These targetIds should be excluded from response
    const targetIds = duration === -1 ? Object.keys(targetsMap) : Object.keys(targetsMap).filter(targetId => (
      targetsMap[targetId].duration === duration && !excludeTargetIds.includes(Number(targetId))));
    let latestTargetDetails = await Promise.all(targetIds.map(targetId => 
      targetService.getLatestTargetById(userId, targetId)
    ))
    latestTargetDetails = latestTargetDetails
      .filter((targetDetails) => targetDetails && targetDetails.isActive === true)
      .reduce((filteredData, targetDetail) => {
        filteredData[targetDetail.targetId] = {
          session: targetDetail?.session || DEFAULT_SESSION_VALUE,
          value: Number(targetDetail?.value || 0),
        }; 
        return filteredData;
      }, {});
    const activeTargetIds = Object.keys(latestTargetDetails).map(Number);
    let targetAchievementsData = [];
    if(pagination) {
      var { limit, page } = parsePaginationParams(req.query);
      from = limit * (page - 1)
      targetAchievementsData = await targetAchievedService.getTargetsAchievedByPagination(userId, from, limit, activeTargetIds);
    }
    else{
      targetAchievementsData = await targetAchievedService.getTargetsAchievedByDateRange(userId, startDate, endDate, activeTargetIds);
    }
    
    const targetDetails = {};
    await Promise.all(targetAchievementsData.map(async (ta) => {
      const targetId = Number(ta.targetId);
      const trackerId = targetsMap[targetId].trackerIds[0];
      const { favorability, name: targetName, unit, duration } = targetsMap[targetId];
      const trackerData = await trackersService.getTrackerDetailsById(trackerId);
      const imageUrl = trackerData?.details?.media?.enabledImageUrl || null;
      if (!targetDetails[targetId]) {
        targetDetails[targetId] = {
          details: {
            userId,
            targetId,
            targetName,
            unit,
            favorability,
            duration,
            trackerId,
            imageUrl,
            session: latestTargetDetails[targetId]?.session || DEFAULT_SESSION_VALUE,
            isMet: false
          },
          dayWiseData: [],
        };
      }
    
      const { id, date, value, target, percentage, isMet } = ta;
      switch(targetId){
        case 19: // BP target
          const { valueDiastole, valueSystole, targetDiastole, targetSystole }  = ta;
          targetDetails[targetId].dayWiseData.push({ id, date, valueDiastole, valueSystole, targetDiastole, targetSystole, percentage, isMet });
          break;
        default:
          targetDetails[targetId].dayWiseData.push({ id, date, value: roundOffNumber(value, 2), target, percentage, isMet });
      }
    }));

    for (const targetId of activeTargetIds) {
      if (!targetDetails[targetId]) {
        const { favorability, name: targetName, unit, duration } = targetsMap[targetId];
        const trackerId = targetsMap[targetId].trackerIds[0];
        const trackerData = await trackersService.getTrackerDetailsById(trackerId);
        const imageUrl = trackerData?.details?.media?.enabledImageUrl || null;
        targetDetails[targetId] = {
          details: {
            userId,
            targetId,
            targetName,
            unit,
            favorability,
            duration,
            trackerId,
            imageUrl,
            session: latestTargetDetails[targetId]?.session || DEFAULT_SESSION_VALUE,
            isMet: false
          },
          dayWiseData: [],
        };
      }
    }  

    // Updating isMet in details according to session value
    for (const targetId in targetDetails) {
      const target = targetDetails[targetId];
      const { session } = target.details;
      const isMetCount = target.dayWiseData.reduce((count, entry) => (entry.isMet ? count + 1 : count), 0);

      if (isMetCount >= session) {
        target.details.isMet = true;
      }
    }

    if (!pagination) {
      // Filling default data if target achieved is not present for a date, only if target duration is 7.
      const currentDate = Math.min(new Date(), new Date(endDate));
      const targetsByDateRange = await targetService.getTargetsByDateRange(userId, startDate, endDate, activeTargetIds);
      const targetIdsTargetValuesMapping = {};
      /**
       * e.g. targetIdTargetsMapping => { "targetId" : {"date": "targetValue"} }
          {
            "1": {
              "2023-01-01": 500,
              "2023-01-02": 600,
              "2023-01-03": 700,
              "2023-01-04": 500,
              "2023-01-05": 600,
            },
            "2": {
              "2023-01-01": 500,
              "2023-01-02": 600,
            },
          }
      */
      for(let i = 0; i < targetsByDateRange.length; i++){
        const targetId = targetsByDateRange[i].targetId;
        const date = targetsByDateRange[i].createdAt.split('T')[0];
        if(!targetIdsTargetValuesMapping[targetId]) targetIdsTargetValuesMapping[targetId] = {};
        if(!targetIdsTargetValuesMapping[targetId][date]) targetIdsTargetValuesMapping[targetId][date] = targetsByDateRange[i].value;
      }
      const targetsOnStartDate = await targetService.getLatestTargetsByDate(userId, activeTargetIds, startDate);
      for (const targetId in targetDetails) {
        const dateCursor = new Date(startDate);
        const dataByTargetId = targetDetails[targetId];
        const dayWiseData = dataByTargetId.dayWiseData;
        if (dataByTargetId.details.duration == 7) {  
          while (dateCursor <= currentDate) {
            const formattedDate = dateCursor.toISOString().split("T")[0];
            let latestTargetTillDate = 0;
            if(formattedDate == startDate){
              const targetData = targetsOnStartDate.find(doc => Number(doc.targetId) == targetId) || null;
              latestTargetTillDate = targetData?.value || 0;
              if(!targetIdsTargetValuesMapping[targetId]) targetIdsTargetValuesMapping[targetId] = {};
              targetIdsTargetValuesMapping[targetId][formattedDate] = latestTargetTillDate;
            }
            else {
              const targetIdMapping = targetIdsTargetValuesMapping[targetId];
              for (const date of Object.keys(targetIdMapping)) {
                if (new Date(date).getTime() <= new Date(formattedDate).getTime()) {
                  latestTargetTillDate = targetIdMapping[date];
                  break;
                }
              } 
            }
            const existingEntry = dayWiseData.find((entry) => entry.date === formattedDate);
            if (!existingEntry) {
              const entry = { id: null, date: formattedDate, value: 0, target: 0, percentage: 0, isMet: false }; 
              entry.target = latestTargetTillDate;
              dayWiseData.push(entry);  
            }
            dateCursor.setDate(dateCursor.getDate() + 1);
          }
        }
        dayWiseData.sort((a, b) => a.date.localeCompare(b.date));
      }
    }
    if (pagination) {
      return res.status(200).json({ success: true, message: `User's target achievements`, data: { targetDetails }});
    }
    return res.status(200).json({ success: true, message: `User's target achievements`, data: { startDate, endDate, targetDetails }});
  } catch (error) {
    logger.error(`getAllTargetsAchieved error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
};

// target achieved externally (eg. meal log)
module.exports.postTargetAchievedById = async function (req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    const targetId = req.params?.targetId;

    if (!userId) {
      return next({ message: "user_id missing in request", statusCode: 400 });
    }
    if (!targetId) {
      return next({ message: "targetId missing in request query", statusCode: 400 });
    }

    let targetData = await targetService.getLatestTargetById(userId, targetId);
    if(targetData && !targetData?.isActive){
      return res.status(200).json({ success: true, message: "Target is inactive", data: [] });
    }

    await targetAchievedService.computeTargetAchieved(userId, trackerId, logId, log, values, filterParam)

    // Default
    return res.status(200).json({success: true, message: `The user have no target achievement`, data: []});
  } catch (error) {
  logger.error(`postTargetAchievedById error: ${error.message}`, error);
  return next({ message: error.message, statusCode: 500 });
  }
};

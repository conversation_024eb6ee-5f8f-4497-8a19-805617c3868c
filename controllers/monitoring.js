const { userService } = require("../service/users");
const { getStaticTrackers, getStaticTrackerDefaultTargetsMapping } = require("../utils/staticData");
const logsService = require("../service/logs");
const { deepCopy } = require("../utils/helpers");
const { getConnectedUserList, getConnectedUserListWithStrategies } = require("../utils/aws");
const { trackersService } = require("../service/trackers");
const { log: logger } = require("../utils/logger");

module.exports.getUserCapturedCount = async function (req, res, next) {
  const requestStartTime = Date.now();
  const requestId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  try {
    const userId = req.query?.doctor_guid || req.decoded.user_guid; //Query applicable only for BUSER

    logger.info({
      requestId,
      function: 'getUserCapturedCount',
      userId,
      endpoint: `GET /trackers/api/v1/monitoring`
    }, 'Starting getUserCapturedCount request');

    if (!userId) {
      logger.warn({ requestId, error: 'user_id missing in request' }, 'Validation failed');
      return next({ message: "user_id missing in request", statusCode: 400 });
    }

    logger.info({ requestId, step: 'date_range_setup' }, 'Setting up date range');
    let endTime = new Date(), startTime = new Date();
    if (req.query?.startDate && req.query?.endDate) {
      startTime = new Date(req.query?.startDate);
      endTime = new Date(req.query?.endDate);
    } else {
      const duration = parseInt(req.query?.duration) || 2;
      startTime.setDate(startTime.getDate() - duration);
    }

    logger.info({
      requestId,
      step: 'date_range_configured',
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: req.query?.duration || 2
    }, 'Date range configured');

    // Check for performance strategy in query params
    const strategy = req.query?.strategy || 'fast'; // fast, immediate_fallback, async_fire_forget

    logger.info({
      requestId,
      step: 'fetching_connected_users',
      userId,
      strategy
    }, 'Starting to fetch connected users with strategy');

    const connectedUsersStartTime = Date.now();
    let connectedUsers;

    if (strategy === 'fast') {
      connectedUsers = await getConnectedUserList(userId, true); // Enable fast mode (3 second timeout)
    } else {
      connectedUsers = await getConnectedUserListWithStrategies(userId, strategy);
    }

    const connectedUsersEndTime = Date.now();

    logger.info({
      requestId,
      step: 'connected_users_fetched',
      userId,
      connectedUsersCount: connectedUsers?.length || 0,
      duration: connectedUsersEndTime - connectedUsersStartTime
    }, 'Connected users fetched');

    let finalConnectedUsers = connectedUsers;

    if(!connectedUsers){
      logger.error({ requestId, userId, error: 'Failed to fetch connected users' }, 'Connected users fetch failed');

      // Intelligent Fallback Strategy:
      // 1. For monitoring purposes, we can assume the requesting user wants to see their own data
      // 2. This allows the API to continue functioning even when the Lambda is down
      // 3. In most cases, users are primarily interested in their own tracking data

      logger.warn({
        requestId,
        userId,
        fallback: 'lambda_timeout_fallback',
        strategy: 'using_requesting_user_only',
        reason: 'ConnectedUsers Lambda function is not responding'
      }, 'Lambda timeout detected - using intelligent fallback strategy');

      finalConnectedUsers = [userId];

      logger.info({
        requestId,
        step: 'fallback_connected_users_applied',
        fallbackUsersCount: finalConnectedUsers.length,
        fallbackStrategy: 'single_user_mode'
      }, 'Fallback strategy applied successfully - API will continue with requesting user only');
    }

    const noOfConnectedUsers = finalConnectedUsers.length;
    const capturedCountsPerTrackerID = {}, uncapturedCountsPerTrackerID = {};

    logger.info({
      requestId,
      step: 'fetching_default_trackers',
      connectedUsersCount: finalConnectedUsers.length
    }, 'Starting to fetch default trackers for all connected users');

    const defaultTrackersStartTime = Date.now();
    const defaultDevicesPerUser = {};
    await Promise.all(finalConnectedUsers.map(async (connectedUserId) => {
      try {
        const response = await trackersService.getAllDefaultTrackers(connectedUserId);
        defaultDevicesPerUser[connectedUserId] = response?.defaultDevices || [];
        logger.debug({
          requestId,
          connectedUserId,
          defaultDevicesCount: response?.defaultDevices?.length || 0
        }, 'Default trackers fetched for user');
      } catch (error) {
        logger.error({
          requestId,
          connectedUserId,
          error: error.message
        }, 'Failed to fetch default trackers for user');
        defaultDevicesPerUser[connectedUserId] = [];
      }
    }));
    const defaultTrackersEndTime = Date.now();

    logger.info({
      requestId,
      step: 'default_trackers_fetched',
      connectedUsersCount: finalConnectedUsers.length,
      duration: defaultTrackersEndTime - defaultTrackersStartTime
    }, 'Default trackers fetched for all connected users');

    logger.info({
      requestId,
      step: 'processing_trackers',
      trackerCount: Object.keys(logsService.trackerMap).length
    }, 'Starting to process trackers for captured/uncaptured counts');

    const trackerProcessingStartTime = Date.now();
    await Promise.all(
      Object.keys(logsService.trackerMap).map(async (id) => {
        if(id != 1){ //id == 1 belongs to meal Log
          try {
            const tracker = logsService.trackerMap[id];
            logger.debug({
              requestId,
              trackerId: id,
              trackerName: tracker.indexName
            }, 'Processing tracker for captured users');

            const capturedUserList = await userService.getUsersIfCaptured(finalConnectedUsers, id, tracker.indexName, startTime.toISOString(), endTime.toISOString(), true, defaultDevicesPerUser);
            const capturedUserCount = capturedUserList.length;
            capturedCountsPerTrackerID[id] = capturedUserCount;
            uncapturedCountsPerTrackerID[id] = noOfConnectedUsers - capturedUserCount;

            logger.debug({
              requestId,
              trackerId: id,
              capturedUserCount,
              uncapturedUserCount: noOfConnectedUsers - capturedUserCount
            }, 'Tracker processing completed');
          } catch (error) {
            logger.error({
              requestId,
              trackerId: id,
              error: error.message
            }, 'Error processing tracker');
            // Set default values on error
            capturedCountsPerTrackerID[id] = 0;
            uncapturedCountsPerTrackerID[id] = noOfConnectedUsers;
          }
        }
      })
    );
    const trackerProcessingEndTime = Date.now();

    logger.info({
      requestId,
      step: 'trackers_processed',
      duration: trackerProcessingEndTime - trackerProcessingStartTime,
      processedTrackers: Object.keys(capturedCountsPerTrackerID).length
    }, 'All trackers processed successfully');
    logger.info({
      requestId,
      step: 'fetching_static_data'
    }, 'Fetching static trackers and targets mapping');

    const staticDataStartTime = Date.now();
    const trackers = await getStaticTrackers();
    const trackerDefaultTargetsMapping = await getStaticTrackerDefaultTargetsMapping();
    const staticDataEndTime = Date.now();

    logger.info({
      requestId,
      step: 'static_data_fetched',
      duration: staticDataEndTime - staticDataStartTime,
      trackersCount: trackers?.length || 0
    }, 'Static data fetched successfully');

    logger.info({
      requestId,
      step: 'building_response'
    }, 'Building final response data');

    let data = deepCopy(trackers);
    data.forEach((d) => {
      d.subCategories = d.subCategories.map((tr) => {
        let result = { ...tr };
        result = { ...result, capturedUserCount: capturedCountsPerTrackerID[tr.id], };
        result = { ...result, uncapturedUserCount: uncapturedCountsPerTrackerID[tr.id], };
        result.defaultTarget = trackerDefaultTargetsMapping[tr.id];
        return result;
      });
    });

    const totalRequestTime = Date.now() - requestStartTime;

    logger.info({
      requestId,
      step: 'request_successful',
      totalDuration: totalRequestTime,
      responseDataCount: data?.length || 0
    }, 'getUserCapturedCount request completed successfully');

    return res.status(200).json({
        success: true,
        data,
    });
  } catch (error) {
    const totalRequestTime = Date.now() - requestStartTime;
    logger.error({
      requestId,
      step: 'request_error',
      error: error.message,
      stack: error.stack,
      totalDuration: totalRequestTime
    }, 'getUserCapturedCount error occurred');
    return next({ message: error.message, statusCode: 500 });
  }
};

module.exports.getUsersIfCaptured = async function (req, res, next) {
  const requestStartTime = Date.now();
  const requestId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  try {
    const userId = req.query?.doctor_guid || req.decoded.user_guid; //Query applicable only for BUSER
    const trackerId = req.params?.trackerId;
    const isLogCaptured = req.query?.isLogCaptured;

    logger.info({
      requestId,
      function: 'getUsersIfCaptured',
      userId,
      trackerId,
      isLogCaptured,
      endpoint: `GET /trackers/api/v1/${trackerId}/users?isLogCaptured=${isLogCaptured}`
    }, 'Starting getUsersIfCaptured request');

    if (!userId) {
      logger.warn({ requestId, error: 'user_id missing in request' }, 'Validation failed');
      return next({ message: "user_id missing in request", statusCode: 400 });
    }
    const tracker = logsService.trackerMap[trackerId]
    if (!tracker) {
      logger.warn({ requestId, trackerId, error: 'Invalid trackerId' }, 'Validation failed');
      return next({ message: "Invalid trackerId", statusCode: 400 });
    }
    if (!isLogCaptured || !['true','false'].includes(isLogCaptured)) {
      logger.warn({ requestId, isLogCaptured, error: 'Invalid isLogCaptured value' }, 'Validation failed');
      return next({ message: "Captured flag missing in request or its value is incorrect", statusCode: 400 });
    }

    logger.info({ requestId, step: 'date_range_setup' }, 'Setting up date range');
    let endTime = new Date(), startTime = new Date();
    if (req.query?.startDate && req.query?.endDate) {
      startTime = new Date(req.query?.startDate);
      endTime = new Date(req.query?.endDate);
    } else {
      const duration = parseInt(req.query?.duration) || 2;
      startTime.setDate(startTime.getDate() - duration);
    }

    logger.info({
      requestId,
      step: 'date_range_configured',
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: req.query?.duration || 2
    }, 'Date range configured');

    // Check for performance strategy in query params
    const strategy = req.query?.strategy || 'immediate_fallback'; // Default to immediate fallback for better performance

    logger.info({
      requestId,
      step: 'fetching_connected_users',
      userId,
      strategy
    }, 'Starting to fetch connected users with strategy');

    const connectedUsersStartTime = Date.now();
    let connectedUsers;

    if (strategy === 'fast') {
      connectedUsers = await getConnectedUserList(userId, true); // Enable fast mode (3 second timeout)
    } else {
      connectedUsers = await getConnectedUserListWithStrategies(userId, strategy);
    }

    const connectedUsersEndTime = Date.now();

    logger.info({
      requestId,
      step: 'connected_users_fetched',
      userId,
      connectedUsersCount: connectedUsers?.length || 0,
      duration: connectedUsersEndTime - connectedUsersStartTime
    }, 'Connected users fetched');

    let finalConnectedUsers = connectedUsers;

    if(!connectedUsers){
      logger.error({ requestId, userId, error: 'Failed to fetch connected users' }, 'Connected users fetch failed');

      // Intelligent Fallback Strategy:
      // 1. For monitoring purposes, we can assume the requesting user wants to see their own data
      // 2. This allows the API to continue functioning even when the Lambda is down
      // 3. In most cases, users are primarily interested in their own tracking data

      logger.warn({
        requestId,
        userId,
        fallback: 'lambda_timeout_fallback',
        strategy: 'using_requesting_user_only',
        reason: 'ConnectedUsers Lambda function is not responding'
      }, 'Lambda timeout detected - using intelligent fallback strategy');

      finalConnectedUsers = [userId];

      logger.info({
        requestId,
        step: 'fallback_connected_users_applied',
        fallbackUsersCount: finalConnectedUsers.length,
        fallbackStrategy: 'single_user_mode'
      }, 'Fallback strategy applied successfully - API will continue with requesting user only');
    }
    logger.info({
      requestId,
      step: 'fetching_default_trackers',
      connectedUsersCount: finalConnectedUsers.length
    }, 'Starting to fetch default trackers for all connected users');

    const defaultTrackersStartTime = Date.now();
    const defaultDevicesPerUser = {};
    await Promise.all(finalConnectedUsers.map(async (connectedUserId) => {
      try {
        const response = await trackersService.getAllDefaultTrackers(connectedUserId);
        defaultDevicesPerUser[connectedUserId] = response?.defaultDevices || [];
        logger.debug({
          requestId,
          connectedUserId,
          defaultDevicesCount: response?.defaultDevices?.length || 0
        }, 'Default trackers fetched for user');
      } catch (error) {
        logger.error({
          requestId,
          connectedUserId,
          error: error.message
        }, 'Failed to fetch default trackers for user');
        defaultDevicesPerUser[connectedUserId] = [];
      }
    }));
    const defaultTrackersEndTime = Date.now();

    logger.info({
      requestId,
      step: 'default_trackers_fetched',
      connectedUsersCount: finalConnectedUsers.length,
      duration: defaultTrackersEndTime - defaultTrackersStartTime
    }, 'Default trackers fetched for all connected users');
    logger.info({
      requestId,
      step: 'calling_getUsersIfCaptured',
      trackerId,
      trackerIndexName: tracker.indexName,
      connectedUsersCount: finalConnectedUsers.length,
      isLogCaptured: isLogCaptured == "true"
    }, 'Starting main getUsersIfCaptured call');

    const getUsersIfCapturedStartTime = Date.now();
    const userIdList = await userService.getUsersIfCaptured(
      finalConnectedUsers,
      trackerId,
      tracker.indexName,
      startTime.toISOString(),
      endTime.toISOString(),
      isLogCaptured == "true" ? true : false,
      defaultDevicesPerUser
    );
    const getUsersIfCapturedEndTime = Date.now();

    logger.info({
      requestId,
      step: 'getUsersIfCaptured_completed',
      userIdListCount: userIdList?.length || 0,
      duration: getUsersIfCapturedEndTime - getUsersIfCapturedStartTime
    }, 'Main getUsersIfCaptured call completed');

    const totalRequestTime = Date.now() - requestStartTime;

    if (userIdList) {
      logger.info({
        requestId,
        step: 'request_successful',
        userIdListCount: userIdList.length,
        totalDuration: totalRequestTime
      }, 'Request completed successfully');

      res.status(200).json({
        success: true,
        result: { userIdList },
      });
    } else {
      logger.warn({
        requestId,
        step: 'request_failed',
        totalDuration: totalRequestTime
      }, 'Request failed - userIdList is null/undefined');

      res.status(200).json({
        success: false,
        message: `Something went wrong!`,
      });
    }
  } catch (error) {
    const totalRequestTime = Date.now() - requestStartTime;
    logger.error({
      requestId,
      step: 'request_error',
      error: error.message,
      stack: error.stack,
      totalDuration: totalRequestTime
    }, 'getUsersIfCaptured error occurred');
    return next({ message: error.message, statusCode: 500 });
  }
};



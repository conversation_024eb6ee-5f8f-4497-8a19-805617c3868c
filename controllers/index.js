var express = require("express");
var router = express.Router();
var authenticatedRouter = express.Router();
authenticatedRouter.use(require("../middleware/auth").jwtValidator);

// Controllers
const fitbit = require("./fitbit");
const dexcom = require("./dexcom");
const oura = require("./oura");
const triggers = require("./triggers");
const devices = require("./devices");
const trackers = require("./trackers");
const monitoring = require("./monitoring");
const targets = require("./targets");

// Users
authenticatedRouter.get("/whoami", require("./user").getUserDetail);

// Sources, Devices & Trackers
authenticatedRouter.get("/sources/:id", require("./sources").getSourceById);
authenticatedRouter.get("/sources", require("./sources").getSources);
authenticatedRouter.get("/devices/:id", devices.getDevicesById);
authenticatedRouter.get("/devices", devices.getConnectedDevices);
authenticatedRouter.post("/devices", devices.connectSDKDevice);
authenticatedRouter.get("/trackers", trackers.getDefaultTrackers);
authenticatedRouter.patch("/trackers", trackers.postDefaultTrackers);

// Hourly Sync (Handles -> Fitbit + Dexcom + Oura)
router.post("/hourlySync", require("./sync").hourlySync);

// Fitbit
authenticatedRouter.get("/fitbit/metadata", fitbit.getMetadata);
router.get("/fitbit/callback", fitbit.callback);
router.get("/fitbit/notification", fitbit.getNotification);
router.post("/fitbit/notification", fitbit.postNotification);
// here "fitbit" should match with source.Fitbit.name as it is being called by SQS for autoSync
authenticatedRouter.get("/fitbit/sync", fitbit.ManualSync); 

// Dexcom
authenticatedRouter.get("/dexcom/metadata", dexcom.getMetadata);
router.get("/dexcom/callback", dexcom.callback);
authenticatedRouter.get("/dexcom/calibrations", dexcom.calibrations);
// here "dexcom" should match with source.Dexcom.name as it is being called by SQS for autoSync
authenticatedRouter.get("/dexcom/sync", dexcom.ManualSync);

// Oura
authenticatedRouter.get("/oura/metadata", oura.getMetadata);
router.get("/oura/callback", oura.callback);
// here "oura" should match with source.Oura.name as it is being called by SQS for autoSync
authenticatedRouter.get("/oura/sync", oura.ManualSync);

// Monitoring
authenticatedRouter.get("/monitoring", monitoring.getUserCapturedCount);

// Triggers
authenticatedRouter.get("/triggers", triggers.getAllTriggers);
authenticatedRouter.get("/triggers/:triggerId", triggers.getTriggerById);
authenticatedRouter.post("/triggers", triggers.createTriggerLog);
authenticatedRouter.put("/triggers", triggers.upsertTrigger);
authenticatedRouter.put("/triggers/event", triggers.createEvent);
authenticatedRouter.patch("/triggers/:triggerId", triggers.updateTrigger);
authenticatedRouter.put("/triggers/event/:triggerId", triggers.createEvent);

// Targets Achieved
authenticatedRouter.get("/targets/achievements", targets.getAllTargetsAchieved);
authenticatedRouter.get("/targets/achievements/:targetId", targets.getTargetAchievedById);
// Targets
router.get("/targets", targets.getAllTargets);
authenticatedRouter.get("/targets", targets.getAllTargets);
authenticatedRouter.get("/targets/:targetId", targets.getTargetById);
authenticatedRouter.post("/targets", targets.setTargetById);

// Rrecommendations
authenticatedRouter.get("/recommendations", require("./recommendations").getRecommendations);

// Wellness Score
authenticatedRouter.get("/wellness-score", require("./wellnessScore").getWellnessScore);

// Tracker Logs
authenticatedRouter.get("/:trackerId", require("./logs").getLogs);
authenticatedRouter.get("/:trackerId/logs/:logId", require("./logs").getLogById);
authenticatedRouter.post("/bulkPost", require("./logs").bulkPost);
authenticatedRouter.post("/:trackerId", require("./logs").postLogs);
authenticatedRouter.delete("/:trackerId/logs/:logId", require("./logs").deleteLogById);
authenticatedRouter.patch("/:trackerId/logs/:logId", require("./logs").updateLogById);
authenticatedRouter.get("/:trackerId/users", monitoring.getUsersIfCaptured);

// Tracker: Activity Summary
// authenticatedRouter.get("/activity-summary/target-hit-rate", activitySummary.getTargetHitRate);

module.exports.router = router;
module.exports.authenticatedRouter = authenticatedRouter;

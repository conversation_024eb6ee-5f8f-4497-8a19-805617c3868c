const { async<PERSON><PERSON><PERSON> } = require("../utils/helpers");
const { log: logger } = require("../utils/logger");
const { getStaticSources, getStaticSourceDevices } = require("../utils/staticData");

module.exports.getSources = asyncHandler(async (req, res, next) => {
  try {
    let userAgent = req.headers?.['user-agent'];
    let trackers = req.query?.trackerIds ? req.query?.trackerIds.split(",") : [];
    trackers = trackers.map(tr => parseInt(tr)).filter(x => x)
    const sources = await getStaticSources();
    let result = Object.keys(sources).map(sourceId => sources[sourceId]);
    if (trackers.length) {
      result = result.filter(
        source => trackers.every(tr => source.trackers.includes(tr))
      )
    }
    if (userAgent == 'ios' || userAgent == 'android') {
      result = result.filter(
        source => source?.platform.includes(userAgent)
      )
    }
    const sourceDevices = await getStaticSourceDevices();
    if (result.length) {
      result.map(s => {
        s["devices"] = sourceDevices[s.id]
      })

      return res.status(200).json({
        success: true,
        data: result,
      });
    }

    res.status(404).json({
      success: false,
      message: `No sources found`,
    });
  } catch (error) {
    logger.error(`Sources | getSources error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get sources", statusCode: 500 });
  }
});

module.exports.getSourceById = asyncHandler(async (req, res, next) => {
  try {
    let userAgent = req.headers?.['user-agent'];
    const sourceId = req.params.id;
    const invalidRegex = /\D/;
    if (!sourceId?.trim() || invalidRegex.test(sourceId)) {
      return next({ message: "invalid or missing sourceId in request", statusCode: 400 });
    }
    const sources = await getStaticSources();
    const allSources = Object.keys(sources).map(sId => sources[sId]);
    let result = allSources.filter(x => x.id == sourceId)
    if (userAgent == 'ios' || userAgent == 'android') {
      result = result.filter(
        source => source?.platform.includes(userAgent)
      )
    }
    const sourceDevices = await getStaticSourceDevices();
    if (result && result.length) {
      data = result[0]
      data["devices"] = sourceDevices[result.id]

      return res.status(200).json({
        success: true,
        data: result,
      });
    }

    res.status(404).json({
      success: false,
      message: `No source found with source Id: ${sourceId}`,
    });
  } catch (error) {
    logger.error(`Sources | getSourceById error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get source by ID", statusCode: 500 });
  }
});

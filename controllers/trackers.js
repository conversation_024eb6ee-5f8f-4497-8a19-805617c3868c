const { asyncH<PERSON><PERSON>, deepCopy, getLocalDateString } = require("../utils/helpers");
const { log: logger } = require("../utils/logger");
const { trackersService } = require("../service/trackers");
const { getStaticTargetsMap, getStaticTrackers } = require("../utils/staticData");
const targetService = require("../service/targets");
const { upsertUTCOffsetValue } = require("../service/userProfile");

const Ajv = require("ajv");
const addFormats = require("ajv-formats");

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);
const validate = ajv.compile(require("../models/trackers.json"));

const targetsAchievedService = require("../service/targets_achieved");

const noDeviceSupportTrackerIds = [1, 4, 20, 21, 22, 23, 24];

module.exports.getDefaultTrackers = asyncHandler(async (req, res, next) => {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    if (!userId) {
      return next({ message: "user_id is missing in request", statusCode: 400 });
    }
    let UTCOffSetMin = 0;
    if (req.headers?.utcoffsetmin !== undefined) {
      UTCOffSetMin = Number(req.headers.utcoffsetmin);
      upsertUTCOffsetValue(userId, UTCOffSetMin);
    }
    else {
      return next({ message: "utcoffsetmin is missing in request", statusCode: 400 });
    }

    const defTrackers = await trackersService.getAllDefaultTrackers(userId);
    const defaultDevices = defTrackers?.defaultDevices || [];

    // Get default targets
    const targetsMap = await getStaticTargetsMap();
    const targetIds = Object.keys(targetsMap)
    let targets = await Promise.all(targetIds.map(targetId =>
      targetService.getLatestTargetById(userId, targetId)
    ))
    // Create Tracker -> Target map
    const trackerTargets = {}
    for (let i = 0; i < targets.length; i++) {
      const targetId = Number(targets[i]?.targetId || (i + 1));
      const target = targetsMap?.[targetId]
      if(target?.isDefault){
        let targetValue = targets[i]?.value ? Number(targets[i]?.value) : null;
        if(targetId == 19){ // BP target
          targetValue = targets[i] ? { systole: targets[i]?.systole, diastole: targets[i]?.diastole } : null;
        }
        if (target && target?.isActive && target?.trackerIds?.length >= 1) {
          trackerTargets[target?.trackerIds[0]] = {
            "defaultTarget": Number(targetId),
            "targetValue": targetValue,
            "unit": target?.unit || null
          }
        }
        else{
          trackerTargets[target?.trackerIds[0]] = {
            "defaultTarget": Number(targetId),
            "targetValue": null,
            "unit": target?.unit || null
          }
        }
      }
    }

    // Get last log saved for the tracker
    const rawTargetAchievents = await targetsAchievedService.getLatestTargetsAchieved(userId, targetIds);
    const todaysDate = getLocalDateString(new Date().toISOString(), UTCOffSetMin);
    const weeklyTargetIds = Object.keys(targetsMap).filter((k) => targetsMap[k].duration === 7);
    const latestTargetAchievements = formatTargetAchievements(rawTargetAchievents, weeklyTargetIds, todaysDate);
    const trackers = await getStaticTrackers();
    let data = deepCopy(trackers);
    data.forEach(d => {
      d.subCategories = d.subCategories.map(tr => {
        let result = {...tr}

        // Get default device associated with the Tracker
        if (noDeviceSupportTrackerIds.includes(tr.id)) {
          result.deviceId = -1;
        } else {
          const matchedDevice = defaultDevices?.find((dev) => dev.trackerId == tr.id);
          result.deviceId = matchedDevice?.deviceId || null;
        }

        if (trackerTargets[tr.id]) {
          const { defaultTarget } = trackerTargets[tr.id];
          result.defaultTarget = defaultTarget;
          result.lastLog = { ...result.lastLog, ...trackerTargets[tr.id], currentValue: latestTargetAchievements[defaultTarget]?.currentValue || null };
        } else {
          // This case will be removed once all the required targets are inserted as result.defaultTarget will never be null;
          result.defaultTarget = null;
          result.lastLog = { ...result.lastLog, targetValue: null, currentValue: null };
        }
        delete result.lastLog.defaultTarget;
        return result;
      })
    })
    return res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error(`Trackers | getDefaultTrackers error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get default trackers", statusCode: 500 });
  }
});

module.exports.postDefaultTrackers = async function (req, res, next) {
  try {
    const { user_guid: userGuid, user_id: userId } = req.decoded;
    if (!userId || !userGuid) {
      return next({
        message: "Invalid params: userId is mandatory field.",
        statusCode: 400,
      });
    }

    const now = new Date()

    const newDoc = {
      userId: userGuid,
      defaultDevices: [req.body],
      updatedAt: now.toISOString()
    };
    console.log(newDoc)
    const valid = validate(newDoc);
    if (!valid) {
        return next({
          message: 'Validation Failed', error: validate.errors, statusCode: 400,
        });
      }

    const insertedId = await trackersService.updateDefaultTrackers(userGuid, newDoc);

    return res.status(200).json({
      success: true,
      message: `Log entry saved`,
      data: { insertedId },
    });
  } catch (error) {
    logger.error(`Trackers | postDefaultTrackers error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to post default trackers", statusCode: 500 });
  }
};

/**
sample output = {
  "4": {
    currentValue: 19680,
  },
  "5": {
    currentValue: 1357200,
  },
  "6": {
    currentValue: 789,
  },
  "8": {
    currentValue: null,
  },
  "10": {
    currentValue: 55,
  }
}

Other values can be added in object like date
*/
function formatTargetAchievements(data, weeklyTargetIds, todaysDate) {
  const formattedData = {};
  data.forEach((item) => {
    const { targetId, value, date, valueDiastole, valueSystole } = item;
    if (targetId === 19) {
      // BP target
      formattedData[targetId] = {
        currentValue: (valueDiastole && valueSystole) ? { diastole: valueDiastole, systole: valueSystole } : null
      };
    } else if(weeklyTargetIds.includes((targetId).toString()) && date == todaysDate) {
      formattedData[targetId] = { currentValue: value };
    }
    else if(!weeklyTargetIds.includes((targetId).toString())) {
      formattedData[targetId] = { currentValue: value };
    }
  });
  return formattedData;
}

#!/bin/bash

# Script to upload environment variables to AWS Secrets Manager
# Usage: ./upload-env-to-secrets.sh [--profile aws-profile] [--env environment]
#
# The script will automatically determine the environment based on the AWS profile name
# unless explicitly specified with --env.
#
# Examples:
# ./upload-env-to-secrets.sh --profile production
# ./upload-env-to-secrets.sh --profile sandbox
# ./upload-env-to-secrets.sh --env production --profile custom-profile

# Default values
AWS_PROFILE="sandbox"
ENV_EXPLICITLY_SET=false
AWS_REGION="us-east-1"

# Function to display help
show_help() {
  echo "Usage: ./upload-env-to-secrets.sh [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  --env VALUE       Explicitly specify the environment (development, sandbox, production)"
  echo "  --profile VALUE   Specify the AWS profile to use (will auto-determine environment if --env not set)"
  echo "  --region VALUE    Specify the AWS region (default: us-east-1)"
  echo "  --help            Display this help message"
  echo ""
  echo "Examples:"
  echo "  ./upload-env-to-secrets.sh --profile production"
  echo "  ./upload-env-to-secrets.sh --profile sandbox"
  echo "  ./upload-env-to-secrets.sh --profile dev"
  echo "  ./upload-env-to-secrets.sh --env production --profile custom-profile"
  echo ""
  echo "AWS SSO Authentication:"
  echo "  If using AWS SSO, make sure to log in first:"
  echo "  aws sso login --profile your-profile-name"
  exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --env)
      ENV="$2"
      ENV_EXPLICITLY_SET=true
      shift 2
      ;;
    --profile)
      AWS_PROFILE="$2"
      shift 2
      ;;
    --region)
      AWS_REGION="$2"
      shift 2
      ;;
    --help)
      show_help
      ;;
    *)
      # If no flag is provided, assume it's the environment (for backward compatibility)
      if [[ "$1" != --* ]]; then
        ENV="$1"
        ENV_EXPLICITLY_SET=true
        shift
        # Check if there's another argument for AWS profile (backward compatibility)
        if [[ $# -gt 0 && "$1" != --* ]]; then
          AWS_PROFILE="$1"
          shift
        fi
      else
        echo "Unknown option: $1"
        echo "Usage: ./upload-env-to-secrets.sh [--env environment] [--profile aws-profile]"
        echo "Try './upload-env-to-secrets.sh --help' for more information."
        exit 1
      fi
      ;;
  esac
done

# Determine environment from AWS profile if not explicitly set
if [ "$ENV_EXPLICITLY_SET" = false ]; then
  case "$AWS_PROFILE" in
    *prod*|*production*)
      ENV="production"
      ;;
    *sandbox*|*dev*|*development*)
      if [[ "$AWS_PROFILE" == *sandbox* ]]; then
        ENV="sandbox"
      else
        ENV="development"
      fi
      ;;
    *)
      ENV="development"
      ;;
  esac
fi

# Set file paths
ENV_FILE="environment/env/.env.${ENV}"
SECRET_NAME="20degrees-trackers-${ENV}"

# Display settings
if [ "$ENV_EXPLICITLY_SET" = true ]; then
  echo "Environment: $ENV (explicitly set)"
else
  echo "Environment: $ENV (auto-determined from AWS profile)"
fi
echo "AWS Profile: $AWS_PROFILE"
echo "AWS Region: $AWS_REGION"
echo "Secret Name: $SECRET_NAME"

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
  echo "Error: Environment file $ENV_FILE not found"
  exit 1
fi

# Convert .env file to JSON
echo "Converting $ENV_FILE to JSON..."
JSON_CONTENT="{"
while IFS= read -r line || [[ -n "$line" ]]; do
  # Skip comments and empty lines
  if [[ ! "$line" =~ ^#.*$ ]] && [[ ! -z "$line" ]]; then
    # Extract key and value
    key=$(echo "$line" | cut -d '=' -f 1)
    value=$(echo "$line" | cut -d '=' -f 2-)

    # Remove quotes from value if present
    value=$(echo "$value" | sed -e 's/^"//' -e 's/"$//')

    # Add to JSON
    JSON_CONTENT+="\"$key\":\"$value\","
  fi
done < "$ENV_FILE"
# Remove trailing comma and close JSON
JSON_CONTENT=${JSON_CONTENT%,}
JSON_CONTENT+="}"

# Create temporary JSON file
TMP_FILE=$(mktemp)
echo "$JSON_CONTENT" > "$TMP_FILE"

# Verify AWS credentials
echo "Verifying AWS credentials for profile '$AWS_PROFILE'..."
if ! aws sts get-caller-identity --profile "$AWS_PROFILE" --region "$AWS_REGION" > /dev/null 2>&1; then
  echo "Error: Unable to verify AWS credentials for profile '$AWS_PROFILE'"
  echo "If you're using AWS SSO, make sure you've logged in with: aws sso login --profile $AWS_PROFILE"
  echo "For traditional AWS credentials, ensure they're configured with: aws configure --profile $AWS_PROFILE"

  # Clean up
  rm "$TMP_FILE"
  exit 1
fi

# Check if secret exists
echo "Checking if secret $SECRET_NAME exists..."
SECRET_EXISTS=false
if aws secretsmanager describe-secret --secret-id "$SECRET_NAME" --region "$AWS_REGION" --profile "$AWS_PROFILE" > /dev/null 2>&1; then
  SECRET_EXISTS=true
fi

# Update or create secret
if [ "$SECRET_EXISTS" = true ]; then
  echo "Updating existing secret $SECRET_NAME..."
  if ! aws secretsmanager update-secret \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" \
    --secret-id "$SECRET_NAME" \
    --secret-string file://"$TMP_FILE"; then

    echo "Error: Failed to update secret $SECRET_NAME"
    rm "$TMP_FILE"
    exit 1
  fi
else
  echo "Creating new secret $SECRET_NAME..."
  if ! aws secretsmanager create-secret \
    --region "$AWS_REGION" \
    --profile "$AWS_PROFILE" \
    --name "$SECRET_NAME" \
    --description "Environment variables for Trackers $ENV environment" \
    --secret-string file://"$TMP_FILE"; then

    echo "Error: Failed to create secret $SECRET_NAME"
    rm "$TMP_FILE"
    exit 1
  fi
fi

# Clean up
rm "$TMP_FILE"

echo "✅ Environment variables successfully uploaded to AWS Secrets Manager as $SECRET_NAME"

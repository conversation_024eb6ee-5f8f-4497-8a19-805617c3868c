const { roundOffNumber, getLocalDateString } = require("./utils/helpers");
const { getStaticSourcesArray } = require("./utils/staticData");

/**
 * Aggregates tracker logs for incrementally captured data
 * @param {array} logs 
 */
async function logAggregation(trackerId, logs, UTCOffSetMin) {

  console.log("Logs fetched:", logs)

  const aggregatedLogs = {};

  // Group logs by sourceIds
  for (const log of logs) {
    const sId = log.sourceId
    if (!aggregatedLogs[sId]) aggregatedLogs[sId] = [];
    aggregatedLogs[sId].push(log)
  }

  console.log("Grouped Logs:", aggregatedLogs)
  const sources = await getStaticSourcesArray();
  for (const sId of Object.keys(aggregatedLogs)) {
    const source = sources.find(s => s.id == sId)
    if (source && source?.increamentalData) {
      console.log("Incremental data computing")
      const date_groups = {};
      for (const log of aggregatedLogs[sId]) {
        const day = getLocalDateString(log.timestamp, UTCOffSetMin)
        if (!date_groups[day]) date_groups[day] = []
        date_groups[day].push(log)
      }

      const fn = aggregationFunctions[sId]?.[trackerId]
      if (fn) {
        aggregatedLogs[sId] = Object.keys(date_groups).map(date => {
          let log = fn(date_groups[date]);
          const timestamp = new Date(date);
          timestamp.setTime(timestamp.getTime() - (UTCOffSetMin * 60 * 1000)); 
          log.timestamp = timestamp.toISOString(); 
          return log;
        })
      }
      // TODO: Hack to handle undefined aggregationFunctions
      else {
        aggregatedLogs[sId] = Object.keys(date_groups).map(date => {
          const timestamp = new Date(date);
          timestamp.setTime(timestamp.getTime() - (UTCOffSetMin * 60 * 1000)); 
          return { ...date_groups[date][0], timestamp: timestamp.toISOString() }
        })
      }
    }
  }

  console.log("aggregatedLogs Logs:", aggregatedLogs);
  let flattenedAggregatedLogs = Object.values(aggregatedLogs).flatMap((value) => value).flat(1);
  // Logs should be sorted in desc order irrespective of sourceId 
  flattenedAggregatedLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  return flattenedAggregatedLogs;
}

const aggregationFunctions = {
  'sourceId': {
    'trackerId': (logs) => {
      let log = { ...logs[0] }
      let value = log.value
      for (const l of logs) {
        value += l.value
      }
      log['value'] = value
      return log
    }
  },
  '-1': {
    '2': (logs) => {
      let log = { ...logs[0] }
      let value = 0
      for (const l of logs) {
        value += l.value
      }
      log['value'] = roundOffNumber(value);
      return log;
    },
    '3': (logs) => {
      let log = { ...logs[0] }
      let distance = 0, elevation = 0, standHours = 0, standMinutes = 0, steps = 0, floors = 0;
      let activityCalories = { totalCalories: 0, bmrCalories: 0, activeCalories: 0 };
      let activityTime = { lightlyActiveSeconds: 0, fairlyActiveSeconds: 0, veryActiveSeconds: 0, totalActiveSeconds: 0 };
      for (const l of logs) {
        distance += l?.distance || 0;
        elevation += l?.elevation || 0;
        standHours += l?.standHours || 0;
        standMinutes += l?.standMinutes || 0;

        activityCalories.totalCalories += l?.activityCalories?.totalCalories || 0;
        activityCalories.bmrCalories += l?.activityCalories?.bmrCalories || 0;
        activityCalories.activeCalories += l?.activityCalories?.activeCalories || 0;

        activityTime.lightlyActiveSeconds += l?.activityTime?.lightlyActiveSeconds || 0;
        activityTime.fairlyActiveSeconds += l?.activityTime?.fairlyActiveSeconds || 0;
        activityTime.veryActiveSeconds += l?.activityTime?.veryActiveSeconds || 0;
        activityTime.totalActiveSeconds += l?.activityTime?.totalActiveSeconds || 0;

        steps += l?.steps || 0;
        floors += l?.floors || 0;
      }
      log['distance'] = roundOffNumber(distance);
      log['elevation'] = roundOffNumber(elevation);
      log['standHours'] = roundOffNumber(standHours);
      log['standMinutes'] = roundOffNumber(standMinutes);
      log['activityCalories'] = activityCalories;
      log['activityTime'] = activityTime;
      log['steps'] = steps;
      log['floors'] = floors;
      return log;
    },
    '5': (logs) => {
      return logs;
    },    
    '11': (logs) => {
      const total = logs.reduce((sum, log) => sum + log?.dailyRmssd || 0, 0);
      const average = logs.length == 0 ? 0 : (total / logs.length);
      return { ...logs[0], dailyRmssd: roundOffNumber(average) };
    },
    '24': (logs) => {
      let log = { ...logs[0] }
      let value = 0
      for (const l of logs) {
        value += l.value
      }
      log['value'] = roundOffNumber(value);
      return log;
    },
    '25': (logs) => {
      const total = logs.reduce((sum, log) => sum + log?.value || 0, 0);
      const average = logs.length == 0 ? 0 : (total / logs.length);
      return { ...logs[0], value: roundOffNumber(average) };
    },
    '6': (logs) => {
      return logs;
    },
    '7': (logs) => {
      return logs;
    },
    '8': (logs) => {
      return logs;
    },
    '9': (logs) => {
      return logs;
    },
    '10': (logs) => {
      return logs;
    },
    '12': (logs) => {
      return logs;
    },
    '13': (logs) => {
      return logs;
    },
    '14': (logs) => {
      return logs;
    },
    '15': (logs) => {
      return logs;
    },
    '16': (logs) => {
      return logs;
    },
    '17': (logs) => {
      return logs;
    },
    '18': (logs) => {
      return logs;
    },
    '19': (logs) => {
      return logs;
    },
    '20': (logs) => {
      return logs;
    },
    '21': (logs) => {
      return logs;
    },
    '22': (logs) => {
      return logs;
    },
    '23': (logs) => {
      return logs;
    },
  },
  '3': {
    '2': (logs) => {
      let log = { ...logs[0] }
      let value = 0
      for (const l of logs) {
        value += l.value
      }
      log['value'] = roundOffNumber(value);
      return log;
    },
    '3': (logs) => {
      let log = { ...logs[0] }
      let distance = 0, elevation = 0, standHours = 0, standMinutes = 0, steps = 0, floors = 0;
      let activityCalories = { totalCalories: 0, bmrCalories: 0, activeCalories: 0 };
      let activityTime = { lightlyActiveSeconds: 0, fairlyActiveSeconds: 0, veryActiveSeconds: 0, totalActiveSeconds: 0 };
      for (const l of logs) {
        distance += l?.distance || 0;
        elevation += l?.elevation || 0;
        standHours += l?.standHours || 0;
        standMinutes += l?.standMinutes || 0;

        activityCalories.totalCalories += l?.activityCalories?.totalCalories || 0;
        activityCalories.bmrCalories += l?.activityCalories?.bmrCalories || 0;
        activityCalories.activeCalories += l?.activityCalories?.activeCalories || 0;

        activityTime.lightlyActiveSeconds += l?.activityTime?.lightlyActiveSeconds || 0;
        activityTime.fairlyActiveSeconds += l?.activityTime?.fairlyActiveSeconds || 0;
        activityTime.veryActiveSeconds += l?.activityTime?.veryActiveSeconds || 0;
        activityTime.totalActiveSeconds += l?.activityTime?.totalActiveSeconds || 0;

        steps += l?.steps || 0;
        floors += l?.floors || 0;
      }
      log['distance'] = roundOffNumber(distance);
      log['elevation'] = roundOffNumber(elevation);
      log['standHours'] = roundOffNumber(standHours);
      log['standMinutes'] = roundOffNumber(standMinutes);
      log['activityCalories'] = activityCalories;
      log['activityTime'] = activityTime;
      log['steps'] = steps;
      log['floors'] = floors;
      return log;
    },
    '5': (logs) => {
      return logs;
    },
    '11': (logs) => {
      const total = logs.reduce((sum, log) => sum + log?.dailyRmssd || 0, 0);
      const average = logs.length == 0 ? 0 : (total / logs.length);
      return { ...logs[0], dailyRmssd: roundOffNumber(average) };
    },
    '24': (logs) => {
      let log = { ...logs[0] }
      let value = 0
      for (const l of logs) {
        value += l.value
      }
      log['value'] = roundOffNumber(value);
      return log;
    },
    '25': (logs) => {
      const total = logs.reduce((sum, log) => sum + log?.value || 0, 0);
      const average = logs.length == 0 ? 0 : (total / logs.length);
      return { ...logs[0], value: roundOffNumber(average) };
    },
    '6': (logs) => {
      return logs;
    },
    '7': (logs) => {
      return logs;
    },
    '8': (logs) => {
      return logs;
    },
    '9': (logs) => {
      return logs;
    },
    '10': (logs) => {
      return logs;
    },
    '12': (logs) => {
      return logs;
    },
    '13': (logs) => {
      return logs;
    },
    '14': (logs) => {
      return logs;
    },
    '15': (logs) => {
      return logs;
    },
    '16': (logs) => {
      return logs;
    },
    '17': (logs) => {
      return logs;
    },
    '18': (logs) => {
      return logs;
    },
    '19': (logs) => {
      return logs;
    },
    '20': (logs) => {
      return logs;
    },
    '21': (logs) => {
      return logs;
    },
    '22': (logs) => {
      return logs;
    },
    '23': (logs) => {
      return logs;
    },
  },
  '4': {
    '6': (logs) => {
      return logs;
    },
    '7': (logs) => {
      return logs;
    },
    '9': (logs) => {
      return logs;
    },
    '10': (logs) => {
      return logs;
    },
    '15': (logs) => {
      return logs;
    },
    '18': (logs) => {
      return logs;
    },
  },
}

module.exports = {
  logAggregation
}

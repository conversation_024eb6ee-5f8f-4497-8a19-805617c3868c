# Project Documentation Memory

## Documentation Created

- Created comprehensive documentation for the 20Degrees Trackers project
- Documentation is organized in the `docs` folder with the following files:
  - `README.md`: Overview of the documentation
  - `architecture.md`: System architecture, components, data flow, and technical design
  - `project.md`: Project overview, features, modules, API endpoints, and development information
  - `data-models.md`: Data models, database schema, and data relationships
  - `api.md`: API endpoints, request/response formats, and authentication

## Documentation Features

- Used Mermaid diagrams to visualize:
  - System architecture
  - Data flow
  - Database schema
  - Deployment architecture
  - Module structure
  - Synchronization process
  - Data relationships

## Project Analysis

- The project is a health data tracking and aggregation platform
- It integrates with various health tracking devices and services (Fitbit, Dexcom, Oura)
- The system collects, processes, and analyzes health data to provide insights
- Key features include:
  - Multi-device integration
  - Health data tracking
  - Target setting and tracking
  - Data synchronization
  - Wellness score calculation
  - Personalized recommendations

## Technical Details

- Backend: Node.js with Express
- Database: OpenSearch (AWS Elasticsearch Service)
- Cloud Services: AWS Lambda, S3, SQS
- Authentication: JWT for API authentication, OAuth for device connections
- API Integration: Fitbit API, Dexcom API, Oura API

## System Architecture

- Microservices architecture with Node.js Express backend
- Can run as standalone server or AWS Lambda function
- Uses OpenSearch for data storage
- Integrates with external health tracking services
- Uses AWS services for cloud infrastructure

## Data Models

- User model for user connections to external services
- Devices model for connected devices
- Trackers model for tracker configurations
- Targets model for health targets
- Various health data models for different metrics

## API Endpoints

- Authenticated endpoints (`/trackers/api/v1/*`)
- Public endpoints (`/trackers/*`)
- Endpoints for users, devices, trackers, targets, and health data
- OAuth endpoints for external service integration

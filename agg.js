// Sample logs array
const logs = [
  { timestamp: '2023-09-25T10:00:00', sourceId: 'sourceId1', trackerId: 'tracker1', value: 10 },
  { timestamp: '2023-09-25T12:00:00', sourceId: 'sourceId1', trackerId: 'tracker1', value: 15 },
  { timestamp: '2023-09-26T09:00:00', sourceId: 'sourceId1', trackerId: 'tracker1', value: 20 },
  { timestamp: '2023-09-26T11:00:00', sourceId: 'sourceId1', trackerId: 'tracker1', value: 25 },
  { timestamp: '2023-09-25T08:00:00', sourceId: 'sourceId2', trackerId: 'tracker2', value: 5 },
  { timestamp: '2023-09-25T14:00:00', sourceId: 'sourceId2', trackerId: 'tracker2', value: 10 },
];

// JSON settings for aggregation
const aggregationSettings = {
  sourceId1: { aggregate: true },
  sourceId2: { aggregate: false },
};

// Create an object to store aggregated logs
const aggregatedLogs = {};

// Iterate through the logs array
for (const log of logs) {
  const { sourceId, trackerId, timestamp, value } = log;
  
  // Check if aggregation is needed for this sourceId
  if (aggregationSettings[sourceId]?.aggregate) {
    // Create a unique key for each log group based on sourceId and trackerId
    const groupKey = `${sourceId}_${trackerId}`;
    
    // Initialize the group if it doesn't exist
    if (!aggregatedLogs[groupKey]) {
      aggregatedLogs[groupKey] = {};
    }
    
    // Extract the day from the timestamp
    const day = timestamp.split('T')[0];
    
    // Initialize the day entry if it doesn't exist
    if (!aggregatedLogs[groupKey][day]) {
      aggregatedLogs[groupKey][day] = 0;
    }
    
    // Add the value to the day's sum
    aggregatedLogs[groupKey][day] += value;
  } else {
    // If no aggregation needed, simply store the log as is
    if (!aggregatedLogs[sourceId]) {
      aggregatedLogs[sourceId] = [];
    }
    aggregatedLogs[sourceId].push(log);
  }
}

console.log(aggregatedLogs)

// If you want the result as an array, you can convert it from the aggregatedLogs object
const resultArray = Object.values(aggregatedLogs);

console.log(resultArray);

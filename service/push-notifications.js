const { sendPushNotification, getConnectedUserList } = require("../utils/aws");
const { log: logger } = require("../utils/logger");

const notificationEventNames = {
  newDeviceConnected: "newDeviceConnected",
  newTargetAssigned: "newTargetAssigned",
};

const notificationDescription = {
  newDeviceConnected: {
    type: "trackers",
    title: "20 Degrees",
    alert: "A user connected with a new device",
  },
  newTargetAssigned: {
    type: "trackers",
    title: "20 Degrees",
    alert: "A new target is assigned",
  },
};

async function sendNotificationToUser(senderUserId, userId, notificationName, customData) {
  logger.info(`sendNotificationToUser: senderUserId=${senderUserId}, userId=${userId}, notificationName="${notificationName}"`);

  try {
    const { alert, title, type } = notificationDescription[notificationName];

    if (!alert || !title || !type) {
      logger.warn(`Missing notification config for "${notificationName}"`);
      return false;
    }
    const flag = await sendPushNotification(senderUserId, userId, alert, title, type, customData);
    logger.info(`sendNotificationToUser result: ${flag}`);
    return flag;
  } catch (error) {
    logger.warn(`Error calling sendNotificationToUser, ${JSON.stringify(error)}`);
    return false;
  }
}

// if userId param belongs to BUSER, then it will send a notification to each connected USERS
// vice versa, if userId param belongs to USER, then it will send a notification to each connected BUSERS
async function sendNotificationToConnectedUsers(userId, notificationName, customData) {
  logger.info(`sendNotificationToConnectedUsers: userId=${userId}, notificationName="${notificationName}"`);

  try {
    const { alert, title, type } = notificationDescription[notificationName];
    const connectedUsers = await getConnectedUserList(userId);
    logger.info(`Connected users count: ${connectedUsers?.length || 0}`);

    if (!alert || !title || !type || !connectedUsers) {
      logger.warn(`Validation failed for sendNotificationToConnectedUsers`);
      return false;
    }
    await Promise.all(connectedUsers.map((buserId) =>
        sendPushNotification(userId, buserId, alert, title, type, customData)
    ));
    logger.info(`sendNotificationToConnectedUsers completed successfully`);
    return true;
  } catch (error) {
    logger.warn(`Error calling sendNotificationToConnectedBUsers, ${JSON.stringify(error)}`);
    return false;
  }
}

module.exports = {
  notificationEventNames,
  notificationDescription,
  sendNotificationToUser,
  sendNotificationToConnectedUsers,
};

const { getClient } = require("../utils/connection");
const utils = require("./utils.js");
const { config } = require("../environment/index");
const { getFutureDateRange } = require("../utils/helpers");
const { getStaticTargetsMap } = require("../utils/staticData");

const indexName = config.INDEX.targetCron;

async function insertData(document) {
  const { userId, targetId } = document;
  const targetsMap = await getStaticTargetsMap();
  // target active window will be started only when weekly target is set as active
  if (targetsMap[targetId]?.duration != 7 || !document.isActive) return;
  
  // Only one entry per userId
  const existingEntry = await getData(userId);
  if (!existingEntry) {
    const { startDate: activeWindowStartDate, endDate: activeWindowEndDate } = getFutureDateRange(new Date(), 6);
    const doc = {
      userId,
      isEnabled: true,
      createdAt: new Date().toISOString(),
      activeWindowStartDate,
      activeWindowEndDate,
    };
    await utils.insertData(indexName, doc);
  }
}

async function getData(userId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ createdAt: { order: "desc" } }],
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }],
        },
      },
      size: 1
    },
  });

  return response.body?.hits?.hits[0]?._source || null;
}

module.exports = {
    insertData,
    getData,
}

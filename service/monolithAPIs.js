const axios = require("axios").default;
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");

const baseUrl = `${config.MONOLITH.SERVER_URL}`;
const userMetadataUrl = `${baseUrl}/api/users/metadata`;
const xApiKey = config.xAPIKey;

async function updateIsDeviceConnected(userId, isDeviceConnected) {
  try {
    let data = {
      is_device_connected: `${isDeviceConnected}`
    }
    const url = `${userMetadataUrl}/${userId}`;
    let config = {
      method: "put",
      url,
      headers: {
        "x-api-key": xApiKey,
      },
      data,
    };

    const response = await axios.request(config);
    logger.info(JSON.stringify(response?.data));
    if (response?.data?.success) {
      logger.info(`Successfully updated is_device_connected flag for user: ${userId}`);
      return true;
    } else {
      logger.warn(`Failed to update is_device_connected flag for user: ${userId}`);
      return false;
    }
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return false;
  }
}

module.exports = { updateIsDeviceConnected };

const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const { getStaticTrackers, getStaticTargetsMap }  = require("../utils/staticData")
const utils = require("./utils.js");
const indexName = config.INDEX.trackers;

async function insertData(document) {
  logger.info({
    service: 'trackers',
    function: 'insertData',
    userId: document.userId
  }, 'Inserting tracker data');
  
  const result = await utils.insertData(indexName, document);
  
  logger.info({
    service: 'trackers',
    function: 'insertData',
    userId: document.userId,
    result
  }, 'Tracker data inserted successfully');
  
  return result;
}

async function getAllDefaultTrackers(userId) {
  logger.info({
    service: 'trackers',
    function: 'getAllDefaultTrackers',
    userId
  }, 'Getting all default trackers');

  try {
    const client = await getClient();
    const response = await client.search({
    index: indexName,
    body: {
      query: { match: { "userId.keyword": userId } },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || {};
  
  logger.info({
    service: 'trackers',
    function: 'getAllDefaultTrackers',
    userId,
    hasData: Object.keys(data).length > 0,
    deviceCount: data.defaultDevices?.length || 0
  }, 'Retrieved default trackers');
  
  return data;
  } catch (error) {
    logger.error({
      service: 'trackers',
      function: 'getAllDefaultTrackers',
      userId,
      error: error.message,
      stack: error.stack
    }, 'Failed to get default trackers');
    throw error;
  }
}

async function updateDefaultTrackers(userId, document) {
  logger.info({
    service: 'trackers',
    function: 'updateDefaultTrackers',
    userId,
    deviceCount: document?.defaultDevices?.length || 0
  }, 'Updating default trackers');

  const client = await getClient();
  try{
    const response = await client.search({
      index: indexName,
      body: {
        query: { match: { "userId.keyword": userId } },
      },
    });
    const id = response.body?.hits?.hits[0]?._id || null;

    if (id) {
      logger.info({
        service: 'trackers',
        function: 'updateDefaultTrackers',
        userId,
        documentId: id,
        action: 'update'
      }, 'Existing document found, updating');

      const data = response.body?.hits?.hits[0]?._source || {};
      const defaultDevices = data?.defaultDevices || [];
      const newDevices = document?.defaultDevices || [];

      const tempMap = {};
      const res = [];
      newDevices.forEach(el => {
        if(!tempMap[el.trackerId]) {
          res.push(el); tempMap[el.trackerId] = 1;
        };
      });
      defaultDevices.forEach(el => {
        if(!tempMap[el.trackerId]) {
          res.push(el); tempMap[el.trackerId] = 1;
        };
      });
      document.defaultDevices = res;

      await client.update({
        index: indexName, id,
        body: { doc: { ...document } }
      });
      
      logger.info({
        service: 'trackers',
        function: 'updateDefaultTrackers',
        userId,
        documentId: id,
        deviceCount: res.length
      }, 'Default trackers updated successfully');
      
      return id;
    } else {
      logger.info({
        service: 'trackers',
        function: 'updateDefaultTrackers',
        userId,
        action: 'insert'
      }, 'No existing document found, creating new one');
      
      return await insertData(document);
    }
  } catch (error) {
    logger.warn("Failed to upsert Trackers", JSON.stringify(error));
  }
}

async function upsertTrackers(userId, document) {
  logger.info({
    service: 'trackers',
    function: 'upsertTrackers',
    userId
  }, 'Upserting trackers');

  const client = await getClient();
  try{
    const response = await client.search({
      index: indexName,
      body: {
        query: { match: { "userId.keyword": userId } },
      },
    });
    const id = response.body?.hits?.hits[0]?._id || null;

    if (id) {
      logger.info({
        service: 'trackers',
        function: 'upsertTrackers',
        userId,
        documentId: id,
        action: 'update'
      }, 'Existing document found, updating');
      
      await client.update({
        index: indexName, id,
        body: { doc: { ...document } }
      });
      
      logger.info({
        service: 'trackers',
        function: 'upsertTrackers',
        userId,
        documentId: id
      }, 'Trackers updated successfully');
      
      return id;
    } else {
      logger.info({
        service: 'trackers',
        function: 'upsertTrackers',
        userId,
        action: 'insert'
      }, 'No existing document found, creating new one');
      
      return await insertData(document);
    }
  } catch (error) {
    logger.warn("Failed to upsert Trackers", JSON.stringify(error));
  }
}

async function getTrackerDetailsById(trackerId) {
  logger.info({
    service: 'trackers',
    function: 'getTrackerDetailsById',
    trackerId
  }, 'Getting tracker details by ID');

  const trackerMap = await getStaticTrackers();
  const foundTracker = trackerMap.flatMap(tracker => tracker.subCategories).find(subCategory => subCategory.id === trackerId);
  
  logger.info({
    service: 'trackers',
    function: 'getTrackerDetailsById',
    trackerId,
    found: !!foundTracker
  }, foundTracker ? 'Tracker found' : 'Tracker not found');
  
  return foundTracker || null;
}

async function getTrackerIdsByCategory(category) {
  logger.info({
    service: 'trackers',
    function: 'getTrackerIdsByCategory',
    category
  }, 'Getting tracker IDs by category');

  const trackerMap = await getStaticTrackers();
  const result = trackerMap.find(item => item.category === category);
  const trackerIds = result?.subCategories?.map(sub => sub.id) || [];
  
  logger.info({
    service: 'trackers',
    function: 'getTrackerIdsByCategory',
    category,
    trackerCount: trackerIds.length
  }, 'Retrieved tracker IDs by category');
  
  return trackerIds;
}

async function getTrackersIdsByTargetIds(targetIds) {
  logger.info({
    service: 'trackers',
    function: 'getTrackersIdsByTargetIds',
    targetIds,
    targetCount: targetIds.length
  }, 'Getting tracker IDs by target IDs');

  const targetMap = await getStaticTargetsMap();
  const trackerIds = [];
  targetIds.forEach(targetId => {
    const entry = targetMap[targetId];
    if (entry && entry.trackerIds) {
      trackerIds.push(...entry.trackerIds);
    }
  });

  const uniqueTrackerIds = trackerIds.filter((id, index) => trackerIds.indexOf(id) === index);
  
  logger.info({
    service: 'trackers',
    function: 'getTrackersIdsByTargetIds',
    targetCount: targetIds.length,
    trackerCount: uniqueTrackerIds.length
  }, 'Retrieved unique tracker IDs from targets');
  
  return uniqueTrackerIds;
}

const trackersService = {
  getAllDefaultTrackers,
  updateDefaultTrackers,
  upsertTrackers,
  getTrackerDetailsById,
  getTrackerIdsByCategory,
  getTrackersIdsByTargetIds,
};

module.exports = {
  trackersService,
};

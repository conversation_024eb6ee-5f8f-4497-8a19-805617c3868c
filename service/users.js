const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const utils = require("./utils.js");
const { getConnectedUserList }  = require("../utils/aws");

const indexName = config.INDEX.user;
const MANUAL_ENTRY_DEVICE_ID = -1;

async function insertUser(document) {
  return await utils.insertData(indexName, document)
}

async function getUserDetails(userId, sourceId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function getUserDetailsBySourceIds(userId, sourceIds) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { terms: { sourceId: sourceIds } }, // multiple values
          ],
        },
      },
    },
  });

  const data = response.body?.hits?.hits || [];
  return data.map(hit => hit._source);
}

async function getUserByState(state, sourceId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { state } }, { match: { sourceId } }],
        },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function getUserBySubscriptionId(subscriptionId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        match: { subscriptionId },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function upsertUserDetails(userId, body, sourceId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });
  const id = response.body?.hits?.hits[0]?._id || null;
  if (!id) {
    return await insertUser({ ...body, userId });
  }
  delete body?.createdAt;
  body.updatedAt = new Date().toISOString();
  const updateResponse = await client.update({
    index: indexName,
    id,
    body: {
      doc: {
        ...body,
      },
    },
  });

  logger.debug({ message: "Updated user details", userId, body });
  return updateResponse.body;
}

async function getAllUsers(sourceId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        match: { sourceId }
      }
    },
    size: 10000
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return itm._source;
    });
    return data;
  }
  return [];
}

async function getUsersIfCaptured(connectedUserList, trackerId, trackerName, startDate, endDate, isLogCaptured, defaultDevicesPerUser) {
  // Get the correct filter parameter for this tracker
  const logsService = require("./logs");
  const tracker = logsService.trackerMap[trackerId];
  let filterParam = tracker?.filterParam || 'timestamp';

  logger.info({
    function: 'getUsersIfCaptured',
    trackerId,
    trackerName,
    filterParam,
    connectedUserCount: connectedUserList.length
  }, 'Starting getUsersIfCaptured with correct filter parameter');

  let capturedUsers = [], uncapturedUsers = [];
  const client = await getClient();

  await Promise.all([...connectedUserList].map(async (userId) => {
    try {
      let query = {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { range: { [filterParam]: { from: startDate, to: endDate } } },
          ],
          should: [
            { match: { deviceId: MANUAL_ENTRY_DEVICE_ID } }
          ],
          minimum_should_match: 1,
        },
      };
      const defaultDevices = defaultDevicesPerUser[userId];
      const defaultDevice = defaultDevices?.filter(x => x.trackerId == trackerId)?.[0]

      if (defaultDevice && defaultDevice?.deviceId) {
        query.bool.should.push({ match: { deviceId: defaultDevice.deviceId } });
      }

      // Try with the primary filter parameter first
      let response;
      try {
        response = await client.search({
          index: trackerName,
          body: {
            _source: { includes: [filterParam] },
            sort: [{ [filterParam]: { order: "desc" } }],
            size: 1,
            query,
          },
        });
      } catch (sortError) {
        // If sorting fails due to mapping issues, try alternative approaches
        logger.warn({
          function: 'getUsersIfCaptured',
          userId,
          trackerId,
          trackerName,
          filterParam,
          error: sortError.message,
          fallback: 'trying_without_sort'
        }, 'Sort failed, trying without sort');

        // Try without sorting first
        try {
          response = await client.search({
            index: trackerName,
            body: {
              _source: { includes: [filterParam, 'date', 'createdAt'] },
              size: 1,
              query,
            },
          });
        } catch (noSortError) {
          // If that fails, try with alternative date fields
          logger.warn({
            function: 'getUsersIfCaptured',
            userId,
            trackerId,
            trackerName,
            error: noSortError.message,
            fallback: 'trying_alternative_date_fields'
          }, 'Query failed, trying with alternative date fields');

          // Try with common date field alternatives
          const alternativeFields = ['date', 'createdAt', 'updatedAt'];
          let alternativeSuccess = false;

          for (const altField of alternativeFields) {
            try {
              const altQuery = {
                bool: {
                  must: [
                    { match: { "userId.keyword": userId } },
                    { range: { [altField]: { from: startDate, to: endDate } } },
                  ],
                  should: [
                    { match: { deviceId: MANUAL_ENTRY_DEVICE_ID } }
                  ],
                  minimum_should_match: 1,
                },
              };

              if (defaultDevice && defaultDevice?.deviceId) {
                altQuery.bool.should.push({ match: { deviceId: defaultDevice.deviceId } });
              }

              response = await client.search({
                index: trackerName,
                body: {
                  _source: { includes: [altField] },
                  size: 1,
                  query: altQuery,
                },
              });

              logger.info({
                function: 'getUsersIfCaptured',
                userId,
                trackerId,
                trackerName,
                alternativeField: altField,
                success: true
              }, 'Successfully used alternative date field');

              alternativeSuccess = true;
              break;
            } catch (altError) {
              logger.debug({
                function: 'getUsersIfCaptured',
                userId,
                trackerId,
                alternativeField: altField,
                error: altError.message
              }, 'Alternative field also failed');
              continue;
            }
          }

          if (!alternativeSuccess) {
            logger.error({
              function: 'getUsersIfCaptured',
              userId,
              trackerId,
              trackerName,
              error: 'All date field alternatives failed'
            }, 'Could not query user data with any date field');

            // Add to uncaptured users if we can't query their data
            uncapturedUsers.push({ userId: userId, lastCaptured: null });
            return;
          }
        }
      }

      const trackerLog = response.body?.hits?.hits || [];
      if (trackerLog.length) {
        capturedUsers.push({ userId: userId, lastCaptured: trackerLog[0]?._source?.date || trackerLog[0]?._source?.[filterParam] || null });
      } else {
        uncapturedUsers.push({ userId: userId, lastCaptured: null });
      }
    } catch (error) {
      logger.error({
        function: 'getUsersIfCaptured',
        userId,
        trackerId,
        trackerName,
        error: error.message
      }, 'Error processing user in getUsersIfCaptured');

      // Add to uncaptured users if there's an error
      uncapturedUsers.push({ userId: userId, lastCaptured: null });
    }
  }));

  return isLogCaptured ? capturedUsers : uncapturedUsers;
}

async function deleteUser(userId, sourceId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });
  const doc = response.body?.hits?.hits[0]?._source || null;
  const id = response.body?.hits?.hits[0]?._id || null;
  if (id && doc) {
    await client.delete({
      index: indexName,
      id,
    });
    // Return deleted document
    return doc;
  }
  return null;
}

const userService = {
  insertUser,
  getUserDetails,
  getUserDetailsBySourceIds,
  getUserByState,
  getUserBySubscriptionId,
  upsertUserDetails,
  getAllUsers,
  deleteUser,
  getUsersIfCaptured
};

module.exports = { userService };

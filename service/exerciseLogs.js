const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const indexName = config.INDEX.exerciseLogs;

async function getLogsByDateRange(userId, startTime, endTime, isComplete, isPlatformSupportedOnly) {
  const client = await getClient();
  let query = {
    bool: {
      must: [
        { match: { "userId.keyword": userId } },
        { range: { timestamp: { gte: startTime, lte: endTime } } },
      ],
    },
  };

  if([true, false].includes(isComplete)) {
    query.bool.must.push({ match: { isComplete } });
  }

  if ([true, false].includes(isPlatformSupportedOnly)) {
    if (isPlatformSupportedOnly) { // Fetch documents with exerciseId not equal to -1
      query.bool.must.push({ range: { exerciseId: { gt: -1 } } });
    } else {
      query.bool.must.push({ term: { exerciseId: -1 } }); // Fetch documents with exerciseId equal to -1
    }
  }

  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ timestamp: {order: "desc"} }],
      query
    },
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source }
    });
    return data;
  }
  return [];
}

module.exports = {
  getLogsByDateRange,
}

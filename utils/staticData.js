const { LambdaClient, InvokeCommand } = require("@aws-sdk/client-lambda");
const { NodeHttpHandler } = require("@aws-sdk/node-http-handler");
const https = require("https");
const { config } = require("../environment/index");
const { log: logger } = require("./logger");
const { checkIfAnyEmpty, deepCopy } = require("../utils/helpers");

// --- Optimized Client and Cache Setup ---

// Create a dedicated HTTPS agent for performance. keepAlive is crucial for Lambda performance.
const httpsAgent = new https.Agent({
  keepAlive: true,
  maxSockets: 25, // A reasonable default for concurrent connections
});

// A single, reusable Lambda client with an optimized connection handler.
const lambdaClient = new LambdaClient({
  region: "us-east-1",
  requestHandler: new NodeHttpHandler({
    httpsAgent: httpsAgent,
    connectionTimeout: 5000,
    socketTimeout: 30000,
  }),
  maxAttempts: 2,
});

// Private, in-memory cache. Stored as an object for efficiency. NOT exported.
const cache = {
  data: null,
  isPopulating: false, // Lock to prevent concurrent fetches during a cold start
};

// --- Generic Lambda Invocation Helper ---

async function invokeLambda(params) {
  const command = new InvokeCommand(params);
  const response = await lambdaClient.send(command);
  const payloadString = Buffer.from(response.Payload).toString('utf-8');
  const payload = payloadString ? JSON.parse(payloadString) : null;
  
  // Handle common Lambda Proxy Integration pattern
  return (payload && typeof payload.body === 'string') ? JSON.parse(payload.body) : payload;
}

// --- Internal Cache Population Logic ---

/**
 * Fetches raw data from the upstream Lambda.
 */
async function fetchStaticDataFromLambda() {
  try {
    const response = await invokeLambda({
      FunctionName: config.AWS.TRACKERS_STATIC_DATA_LAMBDA,
      InvocationType: "RequestResponse",
      Qualifier: "provisioned",
    });
    
    if (response?.success && response.data) {
      logger.info(response.message || "Successfully fetched raw static data from Lambda.");
      return response.data;
    } else {
      logger.warn({ response }, "Lambda fetch failed or returned unsuccessful status.");
      return null;
    }
  } catch (error) {
    logger.error({ err: { message: error.message, stack: error.stack } }, "Exception occurred while fetching static data.");
    return null;
  }
}

/**
 * Pure, synchronous functions to compute derived data. They have one job: transform input to output.
 */
const dataComputations = {
  computeSourcesArray: (sources) => Object.values(sources || {}),

  computeSourceSummary: (sources) => {
    const summary = {};
    for (const sourceId in sources) {
      const { trackers, ...rest } = sources[sourceId];
      summary[sourceId] = rest;
    }
    return summary;
  },

  computeDevicesWithoutManualEntry: (devices) => (devices || []).filter(d => d.id != -1),

  computeSourceDevices: (devices) => {
    return (devices || []).reduce((acc, dev) => {
      const sourceId = dev.sourceId.toString();
      (acc[sourceId] = acc[sourceId] || []).push(dev);
      return acc;
    }, {});
  },

  computeSourcesDevicesMapping: (devices) => {
    return (devices || []).reduce((obj, { sourceId, id }) => {
      (obj[sourceId] = obj[sourceId] || []).push(id);
      return obj;
    }, {});
  },

  computeTrackerDefaultTargetsMapping: (targetsMap) => {
    const mapping = {};
    for (const targetId in targetsMap) {
      const targetDoc = targetsMap[targetId];
      if (targetDoc?.isDefault && targetDoc.trackerIds?.[0]) {
        mapping[targetDoc.trackerIds[0]] = Number(targetId);
      }
    }
    return mapping;
  }
};

/**
 * Populates the cache with fresh data and all computed properties.
 * @returns {Promise<object|null>} The newly populated data object.
 */
async function populateCache() {
  const rawData = await fetchStaticDataFromLambda();
  if (!rawData) return null;

  // Combine raw data with all computed properties
  const fullData = {
    ...rawData,
    sourcesArray: dataComputations.computeSourcesArray(rawData.sources),
    sourceSummary: dataComputations.computeSourceSummary(rawData.sources),
    devicesWithoutManualEntry: dataComputations.computeDevicesWithoutManualEntry(rawData.devices),
    sourceDevices: dataComputations.computeSourceDevices(rawData.devices),
    sourcesDevicesMapping: dataComputations.computeSourcesDevicesMapping(rawData.devices),
    trackerDefaultTargetsMapping: dataComputations.computeTrackerDefaultTargetsMapping(rawData.targetsMap),
  };
  
  cache.data = fullData; // Update the cache
  return cache.data;
}

// --- Public API Functions ---

/**
 * The main gatekeeper function to access static data.
 * Manages cache state and prevents concurrent fetch operations.
 */
async function getStaticData() {
  // If cache is valid, return a deep copy to prevent external mutation
  if (cache.data && !checkIfAnyEmpty(cache.data)) {
    return deepCopy(cache.data);
  }
  
  // If another process is already populating, wait for it to finish.
  // This avoids a "thundering herd" problem on a cold start.
  if (cache.isPopulating) {
    await new Promise(resolve => setTimeout(resolve, 100)); // Short delay
    return getStaticData(); // Re-check the cache
  }

  try {
    cache.isPopulating = true;
    logger.info("Cache is empty or invalid. Populating now...");
    await populateCache();
  } finally {
    cache.isPopulating = false; // Always release the lock
  }

  return cache.data ? deepCopy(cache.data) : null;
}

// All public getters now use the main gatekeeper.
async function getStaticTargetsMap() {
  const data = await getStaticData();
  return data?.targetsMap || {};
}

async function getStaticTrackers() {
  const data = await getStaticData();
  return data?.trackerMap || [];
}

async function getStaticSources() {
  const data = await getStaticData();
  return data?.sources || {};
}

async function getStaticDevices() {
  const data = await getStaticData();
  return data?.devices || [];
}

// The individual getters for computed data also use the same simple pattern.
async function getStaticSourcesArray() {
  const data = await getStaticData();
  return data?.sourcesArray || [];
}

async function getStaticSourceSummary() {
  const data = await getStaticData();
  return data?.sourceSummary || {};
}

async function getStaticDevicesWithoutManualEntry() {
  const data = await getStaticData();
  return data?.devicesWithoutManualEntry || [];
}

async function getStaticSourceDevices() {
  const data = await getStaticData();
  return data?.sourceDevices || {};
}

async function getStaticSourcesDevicesMapping() {
  const data = await getStaticData();
  return data?.sourcesDevicesMapping || {};
}

async function getStaticTrackerDefaultTargetsMapping() {
  const data = await getStaticData();
  return data?.trackerDefaultTargetsMapping || {};
}

// Export ONLY the public API. Do not export the cache or internal helpers.
module.exports = {
  getStaticData,
  getStaticTargetsMap,
  getStaticTrackers,
  getStaticSources,
  getStaticDevices,
  getStaticSourcesArray,
  getStaticSourceSummary,
  getStaticDevicesWithoutManualEntry,
  getStaticSourceDevices,
  getStaticSourcesDevicesMapping,
  getStaticTrackerDefaultTargetsMapping,
};
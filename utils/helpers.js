const { config } = require("../environment/index");
const { log: logger } = require('./logger');
const Ajv = require("ajv");
const addFormats = require("ajv-formats");
const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

const asyncHandler = (fn) => (req, res, next) => {
  return Promise.resolve(fn(req, res, next)).catch(next);
};

const deepCopy = (originalObject) => {
  return JSON.parse(JSON.stringify(originalObject))
}

const getDateAfterSeconds = (seconds) => {
  const date = new Date();
  date.setSeconds(date.getSeconds() + seconds);
  return date;
}

function parsePaginationParams(params) {
  const limit = Number(params.limit);
  const page = Number(params.page);

  if (!limit || limit < 1 || !page || page < 1) {
    page = 1; limit = 20;
  }
  
  return { limit, page };
}

function getNextPrevPagination(endpoint, page, limit, totalDocs) {
  let baseURL = `${config.SERVER_URL}`;

  const next =
    totalDocs >= limit
      ? `${baseURL}/${endpoint}&paginate=true&page=${page + 1}&limit=${limit}`
      : null;

  const previous =
    page > 1
      ? `${baseURL}/${endpoint}&paginate=true&page=${page - 1}&limit=${limit}`
      : null;

  return { next, previous };
};

/**
 * Calculates a date range based on the given date and a specified number of days.
 * @param {string} givenDateTime - The input date in ISO 8601 format (e.g., "2023-08-24").
 * @param {number} noOfDays - The number of days to add to the given date.
 * @returns {object} An object containing the start and end dates of the calculated range.
 *                   For example, if the given date is "2023-08-24" and noOfDays is 6,
 *                   the function will return { startDate: "2023-08-24", endDate: "2023-08-30" }.
 */
function getFutureDateRange(givenDateTime = new Date(), noOfDays = 6) {
  const daysInMilliseconds = Number(noOfDays) * 24 * 60 * 60 * 1000;
  const endDate = new Date(givenDateTime.getTime() + daysInMilliseconds).toISOString().split("T")[0];
  return { startDate: givenDateTime.toISOString().split("T")[0], endDate };
}

/** 
 * Example usage:
 * roundOffNumber(3.14159, 2); // Output: 3.14 (as a number)
 * roundOffNumber("5.6789", 1); // Output: "5.7" (as a string)
 */
function roundOffNumber(num = 0, uptoDigits = 2) {
  num = parseFloat(num);
  const roundedNum = isNaN(num) ? 0 : num.toFixed(uptoDigits);
  return typeof num === 'string' ? roundedNum : parseFloat(roundedNum);
}

/**
 * @param {Number} UTCOffsetMin, offset value from UTC, in minutes e.g. -330 for India
 * @returns startTime & endTime as ISO strings for current day in that time zone
 */
function getDayTimeRange (UTCOffsetMin = 0, timestamp = new Date().toISOString()) {
  const offsetMilliseconds = UTCOffsetMin * 60 * 1000;
  const localMidnight = new Date(new Date(timestamp).getTime() + offsetMilliseconds);
  localMidnight.setUTCHours(0, 0, 0, -offsetMilliseconds); // Set the time to midnight
  const startTime = localMidnight.toISOString();
  localMidnight.setUTCHours(23, 59, 59, 999); // Adjust endTime to the end of the day
  let oneDay = UTCOffsetMin > 0 ? (24 * 60 * 60 * 1000) : 0;
  const endTime = new Date(localMidnight.getTime() + oneDay - offsetMilliseconds).toISOString();
  const timeRange = {startTime, endTime};
  logger.info(`getDayTimeRange | UTCOffsetMin: ${UTCOffsetMin} | timestamp: ${timestamp} | ${JSON.stringify(timeRange)}`);
  return timeRange;
}

function getLocalDateTime(timestamp, UTCOffsetMin) {
  const offsetMilliseconds = UTCOffsetMin * 60 * 1000;
  const localDate = new Date(new Date(timestamp).getTime() + offsetMilliseconds).toISOString();
  return localDate;
}

// timestamp is ISO format
function getLocalDateString(timestamp, UTCOffsetMin) {
  const localDate = new Date(new Date(timestamp).getTime() + UTCOffsetMin * 60000);
  const year = localDate.getFullYear();
  const month = String(localDate.getMonth() + 1).padStart(2, '0');
  const day = String(localDate.getDate()).padStart(2, '0');
  const dateString = `${year}-${month}-${day}`;
  return dateString;
}

function isEmpty(value) {
  if (typeof value === "object" && value !== null) {
    return Array.isArray(value) ? value.length === 0 : Object.keys(value).length === 0;
  }
  return false;
}

function checkIfAnyEmpty(obj) {
  for (let key in obj) {
    if (isEmpty(obj[key])) {
      return true; // Return true if any property is empty
    }
  }
  return false; // If none of the properties are empty, return false
}

// Helper function to validate data/input
function validateData(data, schema) {
  const validate = ajv.compile(schema);
  const valid = validate(data);
  return { valid, errors: validate.errors };
}

// Returns diff between timestamps in seconds
function getDuration(startTimeISO, endTimeISO) {
  try {
    const startTime = new Date(startTimeISO);
    const endTime = new Date(endTimeISO);
    const durationMs = Math.abs(startTime - endTime);
    const duration = durationMs / 1000;
    return duration;
  } catch (error) {
    logger.error("Erron in finding time duration", JSON.stringify(error));
    return 0;
  }
}

function getAdjustedDate(dateString, numberOfDays) {
  const date = new Date(dateString);
  date.setDate(date.getDate() + numberOfDays);
  return date.toISOString().split('T')[0]; // Format the date as 'YYYY-MM-DD'
}

function getUTCDateTimeByLocalDate(dateString, UTCOffsetMin) {
  return getLocalDateTime(new Date(dateString).toISOString(), -UTCOffsetMin); // Negation
}

function getLanguageText(type, language, data) {
  switch (language) {
    case "mn":
      switch (type) {
        case "recommendation_title_form":
          return "Зорилгоо бүртгэ";
        case "recommendation_title_device":
          return "Төхөөрөмж холбох";
        case "recommendation_title_permission":
          return "Зөвшөөрөл хэрэгтэй";
        case "recommendation_title_learn_more":
          return "Таны эрүүл мэндийн оноо";

        case "recommendation_subtitle_form":
          return "Оноогдсон динамик маягтыг бөглөн зорилгоо бүртгэ";
        case "recommendation_subtitle_device":
          return `Өгөгдөл хянахын тулд ${data?.trackerName || ''} төхөөрөмжийг холбох`;
        case "recommendation_subtitle_permission":
          return `${data?.deviceName || ''} төхөөрөмжид ${data?.trackerName || ''} хянагчийн зөвшөөрлийг идэвхжүүлнэ үү`;
        case "recommendation_subtitle_learn_more":
          return "Таны эрүүл мэндийн оноо нь таны эрүүл мэнд, сайн сайхан байдлыг илтгэнэ. Энэ нь биеийн хөдөлгөөн, хоол тэжээл, нойрны чанар, стрессийн түвшин зэрэг янз бүрийн хүчин зүйл дээр суурилдаг.";

        case "recommendation_primary_action_title_form":
          return "Эхлэх";
        case "recommendation_primary_action_title_device":
          return "Одоо холбох";
        case "recommendation_primary_action_title_permission":
          return "Зөвшөөрлийг идэвхжүүлэх";
        case "recommendation_primary_action_title_learn_more":
          return "Илүү ихийг мэдэж аваарай";
        default:
          return "";
      }
    default:
      switch (type) {
        case "recommendation_title_form":
          return "Capture your goal";
        case "recommendation_title_device":
          return "Connect a device";
        case "recommendation_title_permission":
          return "Need permission";
        case "recommendation_title_learn_more":
          return "Your Wellness Score";

        case "recommendation_subtitle_form":
          return "Capture your goals by filling the assigned dynamic form";
        case "recommendation_subtitle_device":
          return `Connect a device to track ${data?.trackerName || '' } data`;
        case "recommendation_subtitle_permission":
          return `Enable permission for ${data?.trackerName || ''} tracker for ${data?.deviceName || ''} device`;
        case "recommendation_subtitle_learn_more":
          return "Your wellness score indicates your overall health and well-being. It's based on various factors such as physical activity, nutrition, sleep quality, and stress levels.";

        case "recommendation_primary_action_title_form":
          return "Start";
        case "recommendation_primary_action_title_device":
          return "Connect now";
        case "recommendation_primary_action_title_permission":
          return "Enable permissions";
        case "recommendation_primary_action_title_learn_more":
          return "Learn more";
        default:
          return "";
      }
  }
}

function getRequestedLanguage(request) {
  var supportedLanguages = ["en", "mn"];
  var defaultLanguage = 'en';
  var language = null;
  var rawHeaders = request.rawHeaders ? request.rawHeaders : [];
  var index = rawHeaders.indexOf('Accept-Language');
  if (index != -1) {
      var headerValue = rawHeaders[index + 1];
      language = headerValue.split("-")[0];
  }
  if (supportedLanguages.indexOf(language) == -1) {
      return defaultLanguage;
  } else {
      return language;
  }
};

function getPathFromUrl(url) {
  const urlObj = new URL(url); // Parse the URL
  const pathWithQuery = urlObj.pathname + urlObj.search; // Get path and query params
  return pathWithQuery.split('/')?.[1] || '';
}

function getFileExtension(path) {
  if (!path) return null;
  const parts = path.split(".");
  return parts.length > 1 ? parts.pop() : null;
}

function getFileType(extension = 'png') {
  const typeMap = {
    jpg: 'image',
    jpeg: 'image',
    png: 'image',
    gif: 'gif',
    mp4: 'video',
    mov: 'video'
  };

  return typeMap[extension] || 'unknown';
}

function sortByDateKeyDesc(items = [], dateKey = 'timestamp') {
  return [...items].sort((a, b) => {
    const aTime = a[dateKey] ? new Date(a[dateKey]).getTime() : -Infinity;
    const bTime = b[dateKey] ? new Date(b[dateKey]).getTime() : -Infinity;
    return bTime - aTime;
  });
}

module.exports = {
  asyncHandler,
  deepCopy,
  validateData,
  checkIfAnyEmpty,
  getDateAfterSeconds,
  parsePaginationParams,
  getNextPrevPagination,
  getFutureDateRange,
  roundOffNumber,
  getDayTimeRange,
  getLocalDateTime,
  getLocalDateString,
  getDuration,
  getAdjustedDate,
  getUTCDateTimeByLocalDate,
  getLanguageText,
  getRequestedLanguage,
  getPathFromUrl,
  getFileExtension,
  getFileType,
  sortByDateKeyDesc,
};

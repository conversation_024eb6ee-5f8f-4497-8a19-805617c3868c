const axios = require("axios").default;
const { config } = require("../environment/index");
const { log: logger } = require("./logger");
const baseUrl = `${config.SERVER_URL}`;

async function assignFirstTargetForm(userId) {
  logger.info(`assignFirstTargetForm for userId=${userId}`);

  try {
    const xAPIKey = config.xAPIKey;
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "x-api-key": xAPIKey },
    };

    const requestUrl = `${baseUrl}/profile/api/v1/forms/target?user_guid=${userId}`;
    const response = await axios.post(requestUrl, null, reqConfig);

    const isSuccessful = response.data?.success || false;
    logger.info(`First target Form Assignment for userId: ${userId}, ${JSON.stringify(response.data)}`);
    logger.info(`assignFirstTargetForm result: ${isSuccessful}`);
    return isSuccessful;
  } catch (error) {
    logger.warn(`assignFirstTargetForm error - status: ${error.response?.status}, data: ${JSON.stringify(error.response?.data)}`);
    logger.warn(JSON.stringify(error));
    return false;
  }
}

async function getAssignedForms(userId, templateId) {
  try {
    const xAPIKey = config.xAPIKey;
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "x-api-key": xAPIKey },
    };

    const response = await axios.get(`${baseUrl}/profile/api/v1/forms?user_guid=${userId}&templateId=${templateId}`, reqConfig);
    const isSuccessful = response.data?.success || false;
    if(isSuccessful) {
      logger.info(`Assigned Forms fetched for userId: ${userId}, ${JSON.stringify(response.data)}`);
      return response.data?.data || [];
    }
    return [];
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return [];
  }
}

module.exports = { 
  assignFirstTargetForm,
  getAssignedForms, 
};

const { Client, Connection } = require("@opensearch-project/opensearch");
const { defaultProvider } = require("@aws-sdk/credential-provider-node");
const aws4 = require("aws4");
const { config } = require("../environment/index");
const { log: logger } = require("./logger");

// Global client instance and initialization promise
let openSearchClientInstance;
let clientInitializationPromise = null;
let lastCredentialsRefresh = 0;
const CREDENTIALS_TTL = 3600000; // 1 hour in milliseconds

// Function to create a custom connection that signs requests with AWS4
function createAwsConnector(credentials, region) {
  class AmazonConnection extends Connection {
    buildRequestObject(params) {
      const request = super.buildRequestObject(params);
      request.service = "es";
      request.region = region;
      request.headers = request.headers || {};
      
      // Ensure host header is set correctly
      if (!request.headers["host"]) {
        request.headers["host"] = request.hostname;
      }

      // Clean up undefined headers
      Object.keys(request.headers).forEach(key => {
        if (request.headers[key] === undefined) {
          delete request.headers[key];
        }
      });

      return aws4.sign(request, credentials);
    }
  }
  return {
    Connection: AmazonConnection,
  };
}

// Function to create the OpenSearch client instance with optimized settings
async function getClientInternal() {
  const initStartTime = Date.now();
  logger.info('Starting OpenSearch client creation process...');

  // Validate configuration
  const awsRegion = config.REGION || process.env.AWS_REGION;
  if (!awsRegion) {
    throw new Error('AWS Region is not configured');
  }
  if (!config.OS_HOST || config.OS_HOST.includes('your-opensearch-domain-endpoint')) {
    throw new Error('OpenSearch host is not configured correctly');
  }

  try {
    // Get AWS credentials with optimized settings
    const credentials = await defaultProvider({
      timeout: 3000, // Reduced timeout for faster failure
      maxRetries: 2
    })();

    // Create client with optimized settings
    const client = new Client({
      ...createAwsConnector(credentials, awsRegion),
      node: config.OS_HOST,
      maxRetries: config.OS_MAX_RETRIES || 2,
      requestTimeout: config.OS_REQUEST_TIMEOUT || 5000,
      pingTimeout: config.OS_PING_TIMEOUT || 3000,
      sniffOnStart: false, // Disable sniffing for Lambda
      sniffOnConnectionFault: false,
      ssl: {
        rejectUnauthorized: true // Enforce SSL verification
      },
      compression: 'gzip',
      keepAlive: true, // Enable connection pooling
      keepAliveMsecs: 30000,
      maxSockets: 50, // Limit concurrent connections
      agent: {
        keepAlive: true,
        maxSockets: 50,
        keepAliveMsecs: 30000
      }
    });

    // Store credentials refresh time
    lastCredentialsRefresh = Date.now();

    const totalDuration = Date.now() - initStartTime;
    logger.info(`OpenSearch client created in ${totalDuration}ms`);
    return client;
  } catch (error) {
    logger.error('Failed to create OpenSearch client:', error);
    throw error;
  }
}

/**
 * Gets a singleton instance of the OpenSearch client with connection reuse.
 * Implements credential refresh and connection health checks.
 */
async function getClient() {
  try {
    // Check if we need to refresh credentials
    const now = Date.now();
    if (openSearchClientInstance && (now - lastCredentialsRefresh) > CREDENTIALS_TTL) {
      logger.info('Refreshing OpenSearch client due to credential expiration');
      openSearchClientInstance = null;
      clientInitializationPromise = null;
    }

    // Return existing client if available
    if (openSearchClientInstance) {
      return openSearchClientInstance;
    }

    // Initialize new client if needed
    if (!clientInitializationPromise) {
      clientInitializationPromise = getClientInternal()
        .then(client => {
          openSearchClientInstance = client;
          return client;
        })
        .catch(err => {
          logger.error('Failed to initialize OpenSearch client:', err);
          clientInitializationPromise = null;
          throw err;
        });
    }

    return clientInitializationPromise;
  } catch (error) {
    logger.error('Error in getClient:', error);
    throw error;
  }
}

// Main Lambda Handler (Example Usage)
exports.handler = async (event, context) => {
  logger.info('Lambda handler invoked. Event:', JSON.stringify(event, null, 2));

  // --- CRITICAL FOR LAMBDA PERFORMANCE ---
  // Set AWS_NODEJS_CONNECTION_REUSE_ENABLED=1 in your Lambda environment variables
  // to enable TCP connection reuse by the underlying Node.js HTTP agent.
  // This is typically set in the Lambda function's configuration, not in code.

  try {
    const osClient = await getClient();

    // Example: Ping the OpenSearch cluster to check connectivity
    logger.info('Pinging OpenSearch cluster...');
    const health = await osClient.cluster.health({});
    logger.info('OpenSearch cluster health:', health.body);

    // Example: Index a document
    const document = {
      id: `doc_${Date.now()}`, // Unique document ID
      title: event.title || 'Test Document from Lambda (AWS4 Signed)',
      content: event.content || 'This is a sample document indexed by an AWS Lambda function using AWS4 signing.',
      timestamp: new Date().toISOString(),
    };
    const indexName = event.indexName || 'my-lambda-aws4-index';

    logger.info(`Indexing document to index '${indexName}'...`, document);
    const indexResponse = await osClient.index({
      index: indexName,
      id: document.id,
      body: document,
      refresh: true, // Make document immediately searchable for testing
    });
    logger.info('Document indexed successfully:', indexResponse.body);


    // Example: Search for documents
    const searchQuery = event.queryStringParameters?.query || 'Lambda';
    logger.info(`Searching index '${indexName}' for query: '${searchQuery}'...`);
    const searchResponse = await osClient.search({
      index: indexName,
      body: {
        query: {
          multi_match: {
            query: searchQuery,
            fields: ['title', 'content'],
          },
        },
      },
    });
    logger.info('Search results:', JSON.stringify(searchResponse.body.hits.hits.map(h => h._source), null, 2));


    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*', // Adjust for your security requirements
      },
      body: JSON.stringify({
        message: 'Successfully connected to OpenSearch using AWS4 signing and performed operations.',
        clusterStatus: health.body.status,
        indexedDocumentResult: indexResponse.body.result,
        searchResultsCount: searchResponse.body.hits.total.value,
        sampleSearchResult: searchResponse.body.hits.hits.length > 0 ? searchResponse.body.hits.hits[0]._source : null,
      }),
    };
  } catch (error) {
    logger.error('Error in Lambda handler:', {
        message: error.message,
        stack: error.stack,
        details: error.meta ? error.meta.body : null // OpenSearch client errors often have details here
    });
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        message: 'Failed to process request due to an internal error.',
        error: error.message,
        errorDetails: error.meta && error.meta.body ? error.meta.body.error : 'No additional details',
      }),
    };
  }
};

// --- Environment Variables for Lambda (Essential) ---
// 1. OPENSEARCH_ENDPOINT: Your OpenSearch domain endpoint (e.g., https://search-mydomain-xxxx.us-east-1.es.amazonaws.com)
// 2. AWS_REGION: (Usually automatically set by Lambda) The AWS region of your OpenSearch domain and Lambda.
// 3. AWS_NODEJS_CONNECTION_REUSE_ENABLED: Set this to '1' in your Lambda environment variables.

// --- IAM Role Permissions Example (Minimal for this handler's operations) ---
/*
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "es:ESHttpGet",    // For cluster.health, getting documents
                "es:ESHttpHead",   // For checking if documents exist
                "es:ESHttpPost",   // For searching, bulk operations
                "es:ESHttpPut"     // For indexing/updating documents
            ],
            // IMPORTANT: Be specific with your domain ARN
            "Resource": "arn:aws:es:YOUR_REGION:YOUR_ACCOUNT_ID:domain/YOUR_OPENSEARCH_DOMAIN_NAME/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
            "Resource": "arn:aws:logs:*:*:*" // For CloudWatch logging
        }
        // If Lambda is in VPC and OpenSearch is in the same VPC, add EC2 permissions:
        // ,
        // {
        //     "Effect": "Allow",
        //     "Action": [
        //         "ec2:CreateNetworkInterface",
        //         "ec2:DescribeNetworkInterfaces",
        //         "ec2:DeleteNetworkInterface"
        //     ],
        //     "Resource": "*" // Restrict if possible
        // }
    ]
}
*/

// To test locally (requires AWS credentials configured for your environment):
// (async () => {
//   if (require.main === module) {
//     // Mock event for local testing
//     const testEvent = {
//       // title: "Local Test Doc",
//       // content: "Content for local test.",
//       // indexName: "local-test-index",
//       queryStringParameters: { query: "Test" }
//     };
//     const testContext = {};
//     try {
//       console.log("Running local test of handler...");
//       const result = await exports.handler(testEvent, testContext);
//       console.log("Local test result:", JSON.stringify(result, null, 2));
//
//       // Test concurrent calls to getClient
//       console.log("Testing concurrent getClient calls (local test)...");
//       const [client1, client2] = await Promise.all([getClient(), getClient()]);
//       console.log(`Client1 === Client2: ${client1 === client2} (should be true)`);
//
//     } catch (e) {
//       console.error("Local test failed:", e);
//     }
//   }
// })();

module.exports = {
  getClient, // Export getClient if you need to use it in other modules (e.g., for tests)
  handler: exports.handler // Explicitly export handler for Lambda
};
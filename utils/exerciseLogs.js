const axios = require("axios").default;
const { config } = require("../environment/index");
const { log: logger } = require("./logger");

const baseUrl = `${config.SERVER_URL}/exercises/api/v1`;

async function addLog(userId, doc) {
  try {
    const xAPIKey = config.xAPIKey;
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "x-api-key": xAPIKey },
    };

    const exerciseId = -1;
    const reqBody = {
      [exerciseId]: {
        exerciseName: doc.activityName,
        totalDuration: doc?.duration || 0, // will be directly in seconds
        calories: doc?.calories || 0,
        timestamp: doc.timestamp
      },
    };

    const response = await axios.post(`${baseUrl}/exercises/logs?user_guid=${userId}`, reqBody, reqConfig);
    const logId = response.data.data?.[0];
    return logId;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return null;
  }
}

module.exports = { addLog };

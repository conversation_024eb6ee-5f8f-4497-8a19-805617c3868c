const { SQSClient, SendMessageCommand } = require("@aws-sdk/client-sqs");
const { LambdaClient, InvokeCommand } = require("@aws-sdk/client-lambda");
const { NodeHttpHandler } = require("@aws-sdk/node-http-handler");
const https = require("https");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");

// --- Centralized Client Creation ---

/**
 * Factory function to create pre-configured AWS clients, reducing boilerplate.
 * @param {new () => any} ClientClass - The AWS SDK client class (e.g., LambdaClient, SQSClient).
 * @param {object} options - Configuration options for the client.
 * @returns {object} A configured client instance.
 */
const createClient = (ClientClass, options) => new ClientClass(options);

const clients = {
  lambda: {
    sync: createClient(LambdaClient, {
      region: "us-east-1",
      maxAttempts: 1,
      requestHandler: new NodeHttpHandler({
        httpsAgent: new https.Agent({ keepAlive: false, maxSockets: 5 }),
        connectionTimeout: 5000,
        socketTimeout: 10000,
      }),
    }),
    async: createClient(LambdaClient, {
      region: "us-east-1",
      maxAttempts: 2,
      requestHandler: new NodeHttpHandler({
        httpsAgent: new https.Agent({ keepAlive: true, keepAliveMsecs: 1000, maxSockets: 5, timeout: 5000 }),
        connectionTimeout: 5000,
        socketTimeout: 15000,
      }),
    }),
  },
  sqs: createClient(SQSClient, {
    region: "us-east-1",
    maxAttempts: 2, // Consistent with other async clients
  }),
};

// --- Generic AWS Service Helpers ---

/**
 * A generic helper to invoke a Lambda function and parse its response.
 * @param {object} params - The parameters for the InvokeCommand.
 * @param {'sync' | 'async'} clientType - The type of client to use ('sync' or 'async').
 * @returns {Promise<any>} The parsed body for 'RequestResponse', or the AWS response for 'Event'.
 */
async function invokeLambda(params, clientType = 'sync') {
  const client = clientType === 'async' ? clients.lambda.async : clients.lambda.sync;
  const command = new InvokeCommand(params);

  const response = await client.send(command);

  if (params.InvocationType === "RequestResponse") {
    const payloadString = Buffer.from(response.Payload).toString('utf-8');
    const payload = payloadString ? JSON.parse(payloadString) : null;
    return (payload && typeof payload.body === 'string') ? JSON.parse(payload.body) : payload;
  }
  return response; // For 'Event' type, return the full response object (e.g., for status code)
}

/**
 * A generic helper to send a message to an SQS queue.
 * @param {string} queueUrl - The URL of the SQS queue.
 * @param {string} messageBody - The body of the message.
 * @param {string} messageGroupId - The MessageGroupId for FIFO queues.
 * @returns {Promise<boolean>} True on success, false on failure.
 */
async function sendSqsMessage(queueUrl, messageBody, messageGroupId) {
  const command = new SendMessageCommand({
    QueueUrl: queueUrl,
    MessageBody: messageBody,
    MessageGroupId: messageGroupId,
  });
  try {
    await clients.sqs.send(command);
    return true;
  } catch (err) {
    logger.error({ err: { message: err.message, stack: err.stack }, queueUrl, messageGroupId }, "Error sending message to SQS");
    return false;
  }
}

// --- Public Service Functions ---

// The `getUserConnections` function is now much simpler.
// It is now an internal helper, as its complexity is handled by callers.
async function getUserConnections(userGuid, useAsync = false) {
  const startTime = Date.now();
  const invocationType = useAsync ? 'Event' : 'RequestResponse';
  logger.info({ function: 'getUserConnections', userGuid, invocationType }, 'Invoking user connections Lambda');

  try {
    const params = {
      FunctionName: config.AWS.USER_CONNECTIONS_LAMBDA,
      InvocationType: invocationType,
      Qualifier: "provisioned",
      Payload: Buffer.from(JSON.stringify({ user_guid: userGuid })),
    };
    const result = await invokeLambda(params, useAsync ? 'async' : 'sync');
    logger.info({ function: 'getUserConnections', userGuid, duration: Date.now() - startTime }, 'Invocation successful');
    return result;
  } catch (error) {
    logger.error({ err: { message: error.message, name: error.name }, userGuid, duration: Date.now() - startTime }, 'getUserConnections failed');
    return null;
  }
}

async function getConnectedUserList(userGuid, fastMode = false) {
  const startTime = Date.now();
  try {
    let responseBody;
    if (fastMode) {
      // The application-level timeout logic belongs here, with the specific use case.
      const fastTimeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Fast mode timeout after 3 seconds')), 3000)
      );
      try {
        responseBody = await Promise.race([getUserConnections(userGuid), fastTimeoutPromise]);
      } catch (error) {
        logger.warn({ function: 'getConnectedUserList', userGuid, mode: 'fast_timeout' }, error.message);
        return null; // Return null on timeout to trigger fallback
      }
    } else {
      responseBody = await getUserConnections(userGuid);
    }

    if (!responseBody) return null;

    // Use optional chaining for safe access to potentially missing data
    const connectedUserIds = responseBody?.result?.map((item) => item.user_guid) || [];
    logger.info({
      function: 'getConnectedUserList',
      userGuid,
      count: connectedUserIds.length,
      duration: Date.now() - startTime,
      mode: fastMode ? 'fast_success' : 'normal',
    }, 'Successfully retrieved connected user list');
    return connectedUserIds;

  } catch (err) {
    logger.error({ err: { message: err.message, stack: err.stack }, userGuid }, 'Error processing connected user list');
    return null;
  }
}

async function sendPushNotification(userGuid, notificationUserGuid, alert, title, type, customData = {}) {
  logger.info({ function: 'sendPushNotification', userGuid, type }, 'Invoking push notification Lambda');
  try {
    const payload = { user_guid: userGuid, notification_user_guid: notificationUserGuid, alert, title, type, custom_data: customData };
    await invokeLambda({
      FunctionName: config.AWS.PUSH_NOTIFICATIONS_LAMBDA,
      InvocationType: 'Event',
      Qualifier: "provisioned",
      Payload: Buffer.from(JSON.stringify(payload)),
    }, 'async');
    return true;
  } catch (error) {
    logger.warn({ err: { message: error.message }, userGuid }, 'Failed to invoke sendPushNotification');
    return false;
  }
}

// --- Simplified SQS Functions ---

async function sendSQSMessage(message) {
  return sendSqsMessage(config.AWS.sqsURL, message, 'TrackersNotification');
}

async function sendTargetComputationSQSMessage(message) {
  return sendSqsMessage(config.AWS.TARGET_COMPUTATION_SQS_URL, message, 'TargetComputation');
}

// --- Strategy Router Function (Largely unchanged, but now calls simpler functions) ---

async function getConnectedUserListWithStrategies(userGuid, strategy = 'fast') {
  switch (strategy) {
    case 'immediate_fallback':
      logger.info({ function: 'getConnectedUserListWithStrategies', userGuid, strategy }, 'Using immediate fallback');
      return null;

    case 'async_fire_forget':
      logger.info({ function: 'getConnectedUserListWithStrategies', userGuid, strategy }, 'Firing Lambda async and using immediate fallback');
      // Fire and forget, no need to wait or handle the result here. Errors are logged inside getUserConnections.
      getUserConnections(userGuid, true);
      return null;

    case 'fast':
    default:
      return getConnectedUserList(userGuid, true); // Use the "fast mode" logic
  }
}

module.exports = {
  sendSQSMessage,
  getUserConnections,
  getConnectedUserList,
  getConnectedUserListWithStrategies,
  sendPushNotification,
  sendTargetComputationSQSMessage,
};
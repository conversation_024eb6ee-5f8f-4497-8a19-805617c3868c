const axios = require("axios").default;
const { config } = require("../environment/index");
const { getPathFromUrl } = require("./helpers");

const dynalinkBaseUrl = config.dynalinks.base_url;
const apiKey = config.dynalinks.apiKey;
const website = config.marketingWebsite;

async function getShortUrl(dynamicLink, path) {
  try {
    const response = await axios.get(
      `https://dynalinks.app/api/v1/links?api_key=${apiKey}&url=${encodeURIComponent(dynamicLink)}`,
      { headers: { "Content-Type": "application/json" } }
    );

    const link = response?.data?.links.find(item => item.path === path) || null;
    return link ? `${dynalinkBaseUrl}/${link.shortened_path}` : null;
  } catch (error) {
    console.log(`Error checking existing short URL & returning it: ${JSON.stringify(error.response?.data || error.message)}`);
    return null;
  }
}

async function createUrlEntry(name, path) {
  try {
    const response = await axios.post(
      `https://dynalinks.app/api/v1/links?api_key=${apiKey}`,
      { name, path, url: `${website}/download`},
      { headers: { "Content-Type": "application/json" } }
    );

    return response?.data?.id || null;
  } catch (error) {
    console.log(`Error creating new entry: ${JSON.stringify(error.response?.data || error.message)}`);
    return null;
  }
}

async function createShortUrl(dynamicUrl) {
  try {
    const response = await axios.post(
      `https://dynalinks.app/api/v1/short_links?api_key=${apiKey}`,
      { url: dynamicUrl },
      { headers: { "Content-Type": "application/json" }, maxBodyLength: Infinity }
    );

    return response?.data?.shortened_path ? `${dynalinkBaseUrl}/${response?.data?.shortened_path}` : null;
  } catch (error) {
    console.error("Error creating short URL:", error.response?.data || error.message);
    return null;
  }
}

async function generateDynamicShortUrl(dynamicLink, name) {
  const path = getPathFromUrl(dynamicLink);
  const existingShortUrl = await getShortUrl(dynamicLink, path);
  if(!existingShortUrl) {
    const isCreated = await createUrlEntry(name, path, dynamicLink);
    const newlyCreatedShortUrl = await createShortUrl(dynamicLink);
    return newlyCreatedShortUrl;
  }
  return existingShortUrl;
}

module.exports = { getShortUrl, createUrlEntry, generateDynamicShortUrl };

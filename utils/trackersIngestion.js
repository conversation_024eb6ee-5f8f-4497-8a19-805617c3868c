const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const { SQSClient, SendMessageCommand } = require("@aws-sdk/client-sqs");
const { config } = require("../environment/index");
const { log: logger } = require("./logger");

// Create AWS SDK v3 clients - let them use default credential resolution
const s3Client = new S3Client({ region: config.REGION });
const sqsClient = new SQSClient({ region: config.REGION });

const S3_BUCKET = config.AWS.TRACKERS_INGESTION_LOGS_S3_BUCKET;
const SQS_URL = config.AWS.TRACKERS_INGESTION_SQS_URL;

async function addLogsToQueue(userId, data, overwrite = false) {
  logger.info(`addLogsToQueue for userId: ${userId}, trackerIdLogsMapping: ${Object.keys(data || {}).length}, overwrite: ${overwrite}`);

  try {
    const refId = Date.now();
    const key = `users/${userId}/tracker_logs/${refId}.json`;

    const s3Data = {
      trackerIdLogsMapping: data,
      overwrite
    }

    await uploadToS3(key, s3Data);
    await sendMessageToSQS({ userId, s3Key: key });

    logger.info(`Posted S3 URL to SQS with Path: ${key}`);
    return true;
  } catch (error) {
    logger.error(`Error in addLogsToQueue: ${JSON.stringify(error)}`);
    return false;
  }
}

async function uploadToS3(key, data) {
  const command = new PutObjectCommand({
    Bucket: S3_BUCKET,
    Key: key,
    Body: JSON.stringify(data),
    ContentType: "application/json",
  });
  const response = await s3Client.send(command);
  logger.info(`Uploaded to S3: ${JSON.stringify(response)}`);
}

async function sendMessageToSQS(message) {
  const command = new SendMessageCommand({
    MessageAttributes: {},
    MessageBody: JSON.stringify(message),
    QueueUrl: SQS_URL,
    MessageGroupId: "TrackersIngestion",
  });
  const response = await sqsClient.send(command);
  logger.info(`sendMessageToSQS: ${JSON.stringify(response)}`);
}

module.exports = {
  addLogsToQueue,
};

# Environment Configuration

This directory contains the environment configuration for the Trackers application.

## Overview

The application uses environment variables for configuration. The configuration loading strategy depends on the deployment environment:

**Local Development:**
- Environment variables are loaded from `.env` files in the `environment/env/` directory
- The appropriate `.env` file is selected based on the `NODE_ENV` environment variable

**Deployed Environments (Lambda):**
- Environment variables are set directly in the AWS Lambda function configuration
- No `.env` files are used; all configuration comes from Lambda environment variables

**Configuration Hierarchy:**
1. Command line arguments (highest priority)
2. Environment variables
3. `.env` files (local development only)

## Directory Structure

- `environment/`
  - `index.js` - Main configuration file that loads environment variables
  - `development.js` - Development environment specific configuration
  - `sandbox.js` - Sandbox environment specific configuration
  - `production.js` - Production environment specific configuration
  - `env/` - Directory containing environment variable files
    - `.env.development` - Development environment variables
    - `.env.sandbox` - Sandbox environment variables
    - `.env.production` - Production environment variables
    - `.env.example` - Example environment variables file

## Environment Variables

The application uses the following environment variables:

### Server Configuration

- `NODE_ENV` - Environment name (development, sandbox, production)
- `OS_HOST` - OpenSearch host URL
- `REGION` - AWS region
- `SERVER_URL` - Server URL
- `PORT` - Server port
- `BASE_PATH` - Base path for the API
- `X_API_KEY` - API key

### Fitbit Configuration

- `FITBIT_AUTHORIZE_URL` - Fitbit OAuth authorization URL
- `FITBIT_API_BASE_URL` - Fitbit API base URL
- `FITBIT_CLIENT_ID` - Fitbit client ID
- `FITBIT_CLIENT_SECRET` - Fitbit client secret
- `FITBIT_SUBSCRIBER_ID` - Fitbit subscriber ID
- `FITBIT_VERIFICATION_CODE` - Fitbit verification code

### Dexcom Configuration

- `DEXCOM_AUTHORIZE_URL` - Dexcom OAuth authorization URL
- `DEXCOM_API_BASE_URL` - Dexcom API base URL
- `DEXCOM_CLIENT_ID` - Dexcom client ID
- `DEXCOM_CLIENT_SECRET` - Dexcom client secret
- `DEXCOM_AUTHORIZE_REDIRECT` - Dexcom OAuth redirect URL

### Oura Configuration

- `OURA_AUTHORIZE_URL` - Oura OAuth authorization URL
- `OURA_API_BASE_URL` - Oura API base URL
- `OURA_CLIENT_ID` - Oura client ID
- `OURA_CLIENT_SECRET` - Oura client secret
- `OURA_AUTHORIZE_REDIRECT` - Oura OAuth redirect URL

### AWS Configuration

- `AWS_SQS_URL` - AWS SQS URL
- `AWS_USER_CONNECTIONS_LAMBDA` - User connections Lambda function name
- `AWS_PUSH_NOTIFICATIONS_LAMBDA` - Push notifications Lambda function name
- `AWS_TRACKERS_STATIC_DATA_LAMBDA` - Trackers static data Lambda function name
- `AWS_TARGET_COMPUTATION_SQS_URL` - Target computation SQS URL
- `AWS_TRACKERS_INGESTION_SQS_URL` - Trackers ingestion SQS URL
- `AWS_TRACKERS_INGESTION_LOGS_S3_BUCKET` - Trackers ingestion logs S3 bucket

### Monolith Configuration

- `MONOLITH_SERVER_URL` - Monolith server URL

### Dynalinks Configuration

- `DYNALINKS_BASE_URL` - Dynalinks base URL
- `DYNALINKS_API_KEY` - Dynalinks API key
- `DYNALINKS_FORMS` - Dynalinks forms URL
- `DYNALINKS_WELLNESS_SCORE` - Dynalinks wellness score URL
- `DYNALINKS_DEVICE_CONNECTION` - Dynalinks device connection URL
- `DYNALINKS_DEVICE_PERMISSION` - Dynalinks device permission URL

### Marketing Website

- `MARKETING_WEBSITE` - Marketing website URL

## Setup

1. Copy `.env.example` to create a new environment file:
   ```
   cp environment/env/.env.example environment/env/.env.development
   ```

2. Edit the environment file with your configuration values.

3. Run the application with the appropriate environment:
   ```
   npm run start:dev     # For development
   npm run start:sandbox # For sandbox
   npm run start:prod    # For production
   ```

## AWS Secrets Manager (Optional)

While the application loads environment variables directly from Lambda configuration, you can optionally use AWS Secrets Manager for secure storage and management of environment variables. This can be useful for:

- Centralized management of environment variables across multiple services
- Secure storage of sensitive configuration data
- Easy rotation of secrets
- Backup and version control of environment configurations

### Upload Environment Variables

Use the provided scripts to upload environment variables to AWS Secrets Manager:

```
npm run upload-env:dev     # Upload development environment variables
npm run upload-env:sandbox # Upload sandbox environment variables
npm run upload-env:prod    # Upload production environment variables
```

You can also use the script directly with more options:

```
# Basic usage with environment flag
scripts/upload-env-to-secrets.sh --env development

# Specify AWS profile
scripts/upload-env-to-secrets.sh --env production --profile prod-profile

# Specify AWS region
scripts/upload-env-to-secrets.sh --env sandbox --region us-west-2

# Auto-determine environment from AWS profile name
scripts/upload-env-to-secrets.sh --profile production
```

The script can automatically determine the environment based on the AWS profile name:
- Profiles containing "prod" or "production" will use the production environment
- Profiles containing "sandbox" will use the sandbox environment
- Profiles containing "dev" or "development" will use the development environment
- All other profiles will default to the development environment

For more information, run:
```
scripts/upload-env-to-secrets.sh --help
```

#### Using with AWS SSO

If you're using AWS SSO for authentication, make sure you've logged in before running the script:

```
# Log in to AWS SSO with your profile
aws sso login --profile your-profile-name

# Then run the upload script with the same profile
scripts/upload-env-to-secrets.sh --profile your-profile-name
```

The script will verify your AWS credentials before attempting to upload the environment variables.

### Retrieve Environment Variables

When deployed as an AWS Lambda function, the application can retrieve environment variables from AWS Secrets Manager.

The secret name follows the pattern: `trackers-{environment}` (e.g., `trackers-production`).

## Lambda Deployment

When deploying the application as an AWS Lambda function:

1. Set the `NODE_ENV` environment variable to the appropriate environment (development, sandbox, production)
2. Set all other required environment variables directly in the Lambda function configuration

The Lambda function will use the environment variables set in its configuration rather than loading from `.env` files or AWS Secrets Manager. This approach is simpler, more performant, and follows AWS best practices.

### Setting Lambda Environment Variables

You can set environment variables in Lambda using:

**AWS CLI:**
```bash
aws lambda update-function-configuration \
  --function-name your-function-name \
  --environment Variables='{
    "NODE_ENV":"production",
    "OS_HOST":"your-opensearch-host",
    "FITBIT_CLIENT_ID":"your-client-id",
    ...
  }'
```

**AWS Console:**
- Go to your Lambda function in the AWS Console
- Navigate to Configuration → Environment variables
- Add/edit the required environment variables

**Infrastructure as Code (Terraform/CloudFormation):**
- Define environment variables in your infrastructure templates
- This is the recommended approach for production deployments

const nconf = require("nconf");
const _ = require('lodash');
const dotenv = require('dotenv');
const path = require('path');

// Determine environment
const nodeEnv = process.env.NODE_ENV || 'development';

// Load environment variables from .env file for local development
// In Lambda/deployed environments, environment variables are set directly in the Lambda configuration
if (process.env.AWS_LAMBDA_FUNCTION_NAME === undefined) {
  const envPath = path.resolve(__dirname, `env/.env.${nodeEnv}`);
  dotenv.config({ path: envPath });
}

// Configure nconf
nconf
    .argv()
    .env();

var all = {
  env: nodeEnv,
  INDEX: {
    water: "water",
    activity: "activity",
    activitySummary: "activity_summary",
    sleep: "sleep",
    bp: "bp",
    bg: "bg",
    egvs: "egvs",
    spo2: "spo2",
    heartRate: "heart_rate",
    restingHeartRate: "resting_heart_rate",
    hrv: "hrv",
    vo2: "vo2",
    ecg: "ecg",
    height: "height",
    weight: "weight",
    fat: "fat",
    bmi: "bmi",
    temp: "temp",
    waistSize: "waist_size",
    hipSize: "hip_size",
    chestSize: "chest_size",
    armSize: "arm_size",
    quadSize: "quad_size",
    mindfulness: "mindfulness",

    user: "user",
    devices: "devices",
    trackers: "trackers",
    triggers: "triggers",

    targets: "user_targets",
    targets_achieved: "user_targets_achievements",

    formsAssigned: "user_forms",

    targetCron: "target_cron",

    userProfiles: "user_profiles",

    exerciseLogs: "exercise_logs",
    mindfulnessLogs: "mindfulness_logs",

    wellness_score: "user_wellness_score",
    recommendations: "user_recommendations",
  },
  wellnessScore: {
    media: {
      gifs: {
        red: {
          light:
            "https://20deg.s3.amazonaws.com/images/wellness-score/red-light.gif",
          dark: "https://20deg.s3.amazonaws.com/images/wellness-score/red-dark.gif",
        },
        blue: {
          light:
            "https://20deg.s3.amazonaws.com/images/wellness-score/blue-light.gif",
          dark: "https://20deg.s3.amazonaws.com/images/wellness-score/blue-dark.gif",
        },
        green: {
          light:
            "https://20deg.s3.amazonaws.com/images/wellness-score/green-light.gif",
          dark: "https://20deg.s3.amazonaws.com/images/wellness-score/green-dark.gif",
        },
      },
    },
  },
  recommendations: {
    form: {
      imageUrl: {
        light: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/form.png",
        dark: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/form.png",
      }
    },
    device: {
      imageUrl: {
        light: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/device-connection.png",
        dark: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/device-connection.png",
      }
    },
    permission: {
      imageUrl: {
        light: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/device-permission.png",
        dark: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/device-permission.png",
      }
    },
    learn_more: {
      imageUrl: {
        light: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/red-light.png",
        dark: "https://20deg.s3.us-east-1.amazonaws.com/images/recommendations/red-dark.png",
      }
    },
  },
  xAPIKey: nconf.get("X_API_KEY") || "",
};

// Load environment-specific configuration
let envConfig = {};
try {
  envConfig = require('./' + all.env + '.js') || {};
} catch (error) {
  console.warn(`No environment-specific config file found for ${all.env}`);
}

const config = _.merge(all, envConfig);

module.exports = { config };
module.exports = {
  OS_HOST: process.env.OS_HOST,
  REGION: process.env.REGION,
  SERVER_URL: process.env.SERVER_URL,
  PORT: parseInt(process.env.PORT || '443', 10),
  BASE_PATH: process.env.BASE_PATH,
  fitbit: {
    authorize_url: process.env.FITBIT_AUTHORIZE_URL,
    api_base_url: process.env.FITBIT_API_BASE_URL,
    client_id: process.env.FITBIT_CLIENT_ID,
    client_secret: process.env.FITBIT_CLIENT_SECRET,
    subscriberId: process.env.FITBIT_SUBSCRIBER_ID,
    verificationCode: process.env.FITBIT_VERIFICATION_CODE,
  },
  dexcom: {
    authorize_url: process.env.DEXCOM_AUTHORIZE_URL,
    api_base_url: process.env.DEXCOM_API_BASE_URL,
    client_id: process.env.DEXCOM_CLIENT_ID,
    client_secret: process.env.DEXCOM_CLIENT_SECRET,
    authorize_redirect: process.env.DEXCOM_AUTHORIZE_REDIRECT
  },
  oura: {
    authorize_url: process.env.OURA_AUTHORIZE_URL,
    api_base_url: process.env.OURA_API_BASE_URL,
    client_id: process.env.OURA_CLIENT_ID,
    client_secret: process.env.OURA_CLIENT_SECRET,
    authorize_redirect: process.env.OURA_AUTHORIZE_REDIRECT
  },
  AWS: {
    sqsURL: process.env.AWS_SQS_URL,
    USER_CONNECTIONS_LAMBDA: process.env.AWS_USER_CONNECTIONS_LAMBDA,
    PUSH_NOTIFICATIONS_LAMBDA: process.env.AWS_PUSH_NOTIFICATIONS_LAMBDA,
    TRACKERS_STATIC_DATA_LAMBDA: process.env.AWS_TRACKERS_STATIC_DATA_LAMBDA,
    TARGET_COMPUTATION_SQS_URL: process.env.AWS_TARGET_COMPUTATION_SQS_URL,
    TRACKERS_INGESTION_SQS_URL: process.env.AWS_TRACKERS_INGESTION_SQS_URL,
    TRACKERS_INGESTION_LOGS_S3_BUCKET: process.env.AWS_TRACKERS_INGESTION_LOGS_S3_BUCKET,
  },
  MONOLITH: {
    SERVER_URL: process.env.MONOLITH_SERVER_URL,
  },
  dynalinks: {
    base_url: process.env.DYNALINKS_BASE_URL,
    apiKey: process.env.DYNALINKS_API_KEY,
    forms: process.env.DYNALINKS_FORMS,
    wellness_score: process.env.DYNALINKS_WELLNESS_SCORE,
    device_connection: process.env.DYNALINKS_DEVICE_CONNECTION,
    device_permission: process.env.DYNALINKS_DEVICE_PERMISSION,
  },
  marketingWebsite: process.env.MARKETING_WEBSITE,
};

{"uuid": "7dba0b4f-ca57-42b9-83da-e076b1d30e24", "lastMigration": 22, "name": "Personal Health", "endpointPrefix": "v1", "latency": 0, "port": 3001, "hostname": "127.0.0.1", "routes": [{"uuid": "7508086c-b332-4f5d-a991-8d11bcfa1bb7", "documentation": "Get all the tracker data sources", "method": "get", "endpoint": "api/v1/sources", "responses": [{"uuid": "c6a57350-03f5-4109-8597-2ac81ee53efb", "body": "{\n  \"success\": true,\n  \"message\": \"available sources\",\n  \"data\": [\n    {\n      \"id\": 53,\n      \"category\": \"FitBit\",\n      \"type\": \"API\",\n      \"name\": \"FitBit Charge 2\",\n      \"isEnabled\": true,\n      \"reSync\": true,\n      \"detail\": {\n        \"description\": \"Track your blood sugar levels with or without a smartphone.\",\n        \"deviceUrl\": \"\",\n        \"supportUrl\": \"\",\n        \"videoUrl\": \"\",\n        \"imageUrl\": \"\"\n      },\n      \"trackerId\": [\n        1,\n        2,\n        3\n      ],\n      \"createdAt\": \"2018-07-20T21:07:13.136Z\",\n      \"updatedAt\": \"2018-07-20T21:07:13.136Z\"\n    },\n    {\n      \"id\": \"83\",\n      \"category\": \"Apple\",\n      \"type\": \"SDK\",\n      \"name\": \"Apple Watch 5\",\n      \"isEnabled\": false,\n      \"reSync\": false,\n      \"detail\": {\n        \"description\": \"Track your blood sugar levels with or without a smartphone.\",\n        \"deviceUrl\": \"\",\n        \"supportUrl\": \"\",\n        \"videoUrl\": \"\",\n        \"imageUrl\": \"\"\n      },\n      \"trackerId\": [\n        1,\n        2\n      ],\n      \"createdAt\": \"2018-07-20T21:07:13.136Z\",\n      \"updatedAt\": \"2018-07-20T21:07:13.136Z\"\n    }\n  ],\n  \"next\":\"\",\n  \"previous\":null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "", "value": "", "operator": "equals", "invert": false}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "24f17348-4fca-47ad-8d3b-b22d10d9b5bf", "documentation": "Get all the tracker data sources", "method": "get", "endpoint": "api/v1/sources/:sourceId/devices", "responses": [{"uuid": "5e65d15c-7519-43e5-8d4f-28a197e5046d", "body": "{\n  \"success\": true,\n  \"message\": \"Details of source\",\n  \"data\": [\n    {\n      \"deviceId\": \"123123\",\n      \"udid\": [\n        \"aqweqweuqwe-12312310-123123\",\n        \"aiwehfuwwew-234234-dgfh43533\"\n      ],\n      \"battery\": \"Medium\",\n      \"batteryLevel\": 60,\n      \"deviceVersion\": \"123.121.2\",\n      \"syncStatus\": {\n        \"status\": \"Completed\",\n        \"details\": \"Successful\",\n        \"syncedAt\": \"2022-07-20T21:07:13.136Z\"\n      }\n    },\n    {\n      \"deviceId\": \"902340\",\n      \"udid\": [\n        \"aqweqweuqwe-12312310-123123\"\n      ],\n      \"battery\": \"High\",\n      \"batteryLevel\": 80,\n      \"deviceVersion\": \"123.120.4\",\n      \"syncStatus\": {\n        \"status\": \"Failed\",\n        \"details\": \"Invalid Credentials\",\n        \"syncedAt\": \"2022-07-20T21:07:13.136Z\"\n      }\n    }\n  ],\n  \"next\":\"\",\n  \"previous\":null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "", "value": "", "operator": "equals", "invert": false}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "5c77e4e3-11b7-4f94-ae8d-1855eb19ad75", "documentation": "Get all the trackers list", "method": "get", "endpoint": "api/v1/trackers", "responses": [{"uuid": "aef797fb-6b34-4beb-9190-50b8101c2cf7", "body": "{\n  \"success\": true,\n  \"message\": \"supported trackers by the service\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"type\": \"activities\",\n      \"name\": \"Steps\",\n      \"category\": \"Activity\",\n      \"imageUrl\": \"\",\n      \"count\": 56\n    },\n    {\n      \"id\": 2,\n      \"type\": \"body_measurements\",\n      \"name\": \"Weight\",\n      \"category\": \"Body Measurements\",\n      \"imageUrl\": \"\",\n      \"count\": 8\n    },\n    {\n      \"id\": 3,\n      \"type\": \"nutrition\",\n      \"name\": \"Food\",\n      \"category\": \"Nutrition\",\n      \"imageUrl\": \"\",\n      \"count\": 12\n    },\n    {\n      \"id\": 4,\n      \"type\": \"sleep\",\n      \"name\": \"Sleep\",\n      \"category\": \"Sleep\",\n      \"imageUrl\": \"\",\n      \"count\": 0\n    },\n    {\n      \"id\": 5,\n      \"type\": \"vitals\",\n      \"name\": \"Blood Pressure\",\n      \"category\": \"Vitals\",\n      \"imageUrl\": \"\",\n      \"count\": 7\n    }\n  ]\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "3126df1b-4dac-401d-875d-ee11bab535a1", "documentation": "Get all the activities with detail logs", "method": "get", "endpoint": "api/v1/trackers/:trackerId/logs", "responses": [{"uuid": "6d21a26c-7180-47e6-965a-499ea90c8395", "body": "{\n  \"success\": true,\n  \"message\": \"Tracker logs\",\n  \"data\": [\n    {\n      \"date\": \"2022-06-09T06:29:59.000Z\",\n      \"total\": \"6500\",\n      \"unit\": \"steps\",\n      \"targetRatio\": 0.65,\n      \"targetRating\":\"Good\",\n      \"logs\": [\n        {\n          \"id\": 123,\n          \"createdAt\": \"2022-06-09T06:29:59.000Z\",\n          \"updatedAt\": \"2022-06-09T06:29:59.000Z\",\n          \"value\": 6500,\n          \"unit\": \"steps\",\n          \"deviceId\": \"12311\",\n          \"sourceId\": 3,\n          \"manual\": false,\n          \"detail\": {}\n        }\n      ]\n    },\n    {\n      \"date\": \"2022-06-08T06:29:59.000Z\",\n      \"total\": \"13000\",\n      \"unit\": \"steps\",\n      \"targetRatio\": 1.3,\n      \"targetRating\":\"Good\",\n      \"logs\": [\n        {\n          \"id\": 109,\n          \"createdAt\": \"2022-06-08T06:29:59.000Z\",\n          \"updatedAt\": \"2022-06-08T06:29:59.000Z\",\n          \"value\": 6500,\n          \"unit\": \"steps\",\n          \"deviceId\": \"12311\",\n          \"sourceId\": 3,\n          \"manual\": false,\n          \"detail\": {}\n        },\n        {\n          \"id\": 110,\n          \"createdAt\": \"2022-06-08T06:29:59.000Z\",\n          \"updatedAt\": \"2022-06-08T06:29:59.000Z\",\n          \"value\": 5500,\n          \"unit\": \"steps\",\n          \"deviceId\": \"12311\",\n          \"sourceId\": 3,\n          \"manual\": false,\n          \"detail\": {}\n        }\n      ]\n    },\n    {\n      \"date\": \"2022-06-07T06:29:59.000Z\",\n      \"total\": \"\",\n      \"unit\": \"\",\n      \"targetRatio\": 1,\n      \"targetRating\":\"Good\",\n      \"logs\": [\n        {\n          \"id\": 124,\n          \"createdAt\": \"2022-06-07T06:29:59.000Z\",\n          \"updatedAt\": \"2022-06-07T06:29:59.000Z\",\n          \"value\": 5000,\n          \"unit\": \"steps\",\n          \"deviceId\": \"12411\",\n          \"sourceId\": 1,\n          \"manual\": true,\n          \"detail\": {}\n        },\n        {\n          \"id\": 125,\n          \"createdAt\": \"2022-06-07T07:29:59.000Z\",\n          \"updatedAt\": \"2022-06-08T08:29:59.000Z\",\n          \"value\": 5000,\n          \"unit\": \"steps\",\n          \"deviceId\": \"12411\",\n          \"sourceId\": 3,\n          \"manual\": false,\n          \"detail\": {}\n        }\n      ]\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "98c629c8-f9ca-4dcd-81b8-6763708dd4e6", "documentation": "Get all the reminders by self", "method": "get", "endpoint": "api/v1/reminders", "responses": [{"uuid": "09b84996-c7b0-4bcc-94e8-2b8e0b3d8e22", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"trackerId\": 1,\n      \"createdAt\": \"2022-06-09T06:29:59.000Z\",\n      \"updatedAt\": \"2022-06-09T22:41:27.141853Z\",\n      \"isOwner\": true,\n      \"start\": \"2022-05-31T04:30:00.000Z\",\n      \"end\": \"2022-05-31T04:30:00.000Z\",\n      \"cron\": \"0 0 0 ? * SUN,TUE,THU *\",\n      \"hasPermissions\": true,\n      \"isEnabled\": true,\n      \"expires\": null,\n      \"createdBy\": \"f1d424ab-f288-42d3-ac9e-3512c5a011e6\"\n    },\n    {\n      \"id\": 2,\n      \"trackerId\": 2,\n      \"createdAt\": \"2022-06-09T06:29:59.000Z\",\n      \"updatedAt\": \"2022-06-09T22:41:27.141853Z\",\n      \"isOwner\": true,\n      \"start\": \"2022-05-31T04:30:00.000Z\",\n      \"end\": \"2022-05-31T04:30:00.000Z\",\n      \"cron\": \"0 0 0 ? * 1/2 *\",\n      \"hasPermissions\": true,\n      \"isEnabled\": true,\n      \"expires\": null,\n      \"createdBy\": \"f1d424ab-f288-42d3-ac9e-3512c5a011e6\"\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "51971f49-2dfe-40b7-b935-2d0ee69b6469", "documentation": "Get all the activity targets by self", "method": "get", "endpoint": "api/v1/targets", "responses": [{"uuid": "bdaa1971-5c9e-46b7-95f3-b73df7009d1e", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"trackerId\": 1,\n      \"createdAt\": \"2022-06-09T06:29:59.000Z\",\n      \"updatedAt\": \"2022-06-09T22:41:27.141853Z\",\n      \"isOwner\": false,\n      \"hasPermissions\": true,\n      \"isEnabled\": true,\n      \"value\": \"10000\",\n      \"unit\": \"steps\",\n      \"createdBy\": \"f1d424ab-f288-42d3-ac9e-3512c5a011e6\"\n    },\n    {\n      \"id\": 2,\n      \"trackerId\": 3,\n      \"createdAt\": \"2022-06-09T06:29:59.000Z\",\n      \"updatedAt\": \"2022-06-09T22:41:27.141853Z\",\n      \"is_owner\": false,\n      \"hasPermissions\": true,\n      \"isEnabled\": true,\n      \"value\": \"510\",\n      \"unit\": \"mins\",\n      \"createdBy\": \"1135d28f-d52e-47b0-a226-5caf64e932d2\"\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "488909b8-e902-4778-b7f2-eaa491401c58", "documentation": "Get all the activity goals by self", "method": "get", "endpoint": "api/v1/goals", "responses": [{"uuid": "350618ec-5002-4449-9fb4-be6cf92d4916", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"targetId\": [\n        1,\n        2,\n        3\n      ],\n      \"trackerId\": [\n        1,\n        2,\n        3,\n        4,\n        5\n      ],\n      \"type\": \"Lose\",\n      \"start\": \"2020-01-01T13:00:00.126Z\",\n      \"baseline\": {\n        \"value\": \"200\",\n        \"unit\": \"lbs\"\n      },\n      \"goal\": {\n        \"value\": \"180\",\n        \"unit\": \"lbs\"\n      },\n      \"threshold\": 0.05,\n      \"createdAt\": \"2022-07-01T13:00:00.126Z\",\n      \"updatedAt\": \"2022-07-01T13:00:00.126Z\"\n    },\n    {\n      \"id\": 2,\n      \"targetId\": [\n        1,\n        2,\n        3\n      ],\n      \"trackerId\": [\n        1,\n        2,\n        3,\n        4,\n        5\n      ],\n      \"type\": \"Build\",\n      \"start\": \"2020-01-01T13:00:00.126Z\",\n      \"baseline\": {\n        \"value\": \"160\",\n        \"unit\": \"lbs\"\n      },\n      \"goal\": {\n        \"value\": \"180\",\n        \"unit\": \"lbs\"\n      },\n      \"threshold\": 0.1,\n      \"createdAt\": \"2022-07-01T13:00:00.126Z\",\n      \"updatedAt\": \"2022-07-01T13:00:00.126Z\"\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "d4530677-39a1-4f72-9bb2-d659cb38ad3e", "documentation": "Get personal Information ", "method": "get", "endpoint": "api/v1/whoami", "responses": [{"uuid": "4193dfd4-03b3-4ac2-8fe3-e8a15034a3ea", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"data\": [\n    {\n      \"id\": \"0b957845-fbc4-471b-b73c-d74d25fb6304\",\n      \"demographics\": {\n        \"firstName\": \"Vivek\",\n        \"lastName\": \"Test\",\n        \"displayName\": \"Vivek Test\",\n        \"avatar\": \"imageUrl\",\n        \"age\": \"28\",\n        \"gender\": \"male\"\n      },\n      \"profile\": {\n        \"role\": \"client\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"...8658\",\n        \"isCorporate\": true,\n        \"tenant\": \"cognizant\",\n        \"signature\": \"https://india-tech-dev.s3.ap-southeast-1.amazonaws.com/images/users/1094/f2a27feb-b779-4e90-bf66-41d75af254dd.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASXZ3C6L4DLRVRIOJ%2F20220609%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20220609T112812Z&X-Amz-Expires=600&X-Amz-Signature=11a29741d3e3c4e9fd846226fc8636f8a54c2b78d1375eee078a76384ed10515&X-Amz-SignedHeaders=host\"\n      },\n      \"license\": {\n        \"isPremium\": \"true\",\n        \"type\": \"monthly\",\n        \"start\": \"2022-11-30T06:45:42.49\",\n        \"end\": \"2022-12-31T06:45:42.49\"\n      },\n      \"lastActive\": \"4Z2022-06-09T06:45:42.49\",\n      \"status\": \"AVAILABLE\",\n      \"deviceDetail\": [\n        {\n          \"udid\": \"aqweqweuqwe-12312310-123123\",\n          \"appVersion\": \"2.18.0\"\n        },\n        {\n          \"udid\": \"aqweqweuqwe-12312310-123123\",\n          \"appVersion\": \"2.17.4\"\n        }\n      ]\n    }\n  ]\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "f1f4e1ea-7864-4699-bbd1-8788fd8bbca1", "documentation": "Get all the videos by self", "method": "get", "endpoint": "api/v1/videos", "responses": [{"uuid": "a5aff613-0e7b-49ba-9aab-306ab555f608", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"count\":15,\n  \"data\": [\n    {\n      \"trackerId\": [\n        1,\n        4,\n        5\n      ],\n      \"videoUrl\": \"https://india-tech-dev.s3.ap-southeast-1.amazonaws.com/47343681/ea63b336-f1e5-46c8-a204-9b48ae795653/recording.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASXZ3C6L4DLRVRIOJ%2F20220610%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20220610T110631Z&X-Amz-Expires=600&X-Amz-Signature=0ece900f77769fb3416ec2311504cab431b19e5e540827196969e4bd5d9ef790&X-Amz-SignedHeaders=host\",\n      \"thumbnail\": \"https://india-tech-dev.s3.ap-southeast-1.amazonaws.com/47343681/ea63b336-f1e5-46c8-a204-9b48ae795653/recording.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASXZ3C6L4DLRVRIOJ%2F20220610%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20220610T110631Z&X-Amz-Expires=600&X-Amz-Signature=0ece900f77769fb3416ec2311504cab431b19e5e540827196969e4bd5d9ef790&X-Amz-SignedHeaders=host\",\n      \"transcript\": \"https://india-tech-dev.s3.ap-southeast-1.amazonaws.com/47343681/ea63b336-f1e5-46c8-a204-9b48ae795653/recording.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASXZ3C6L4DLRVRIOJ%2F20220610%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20220610T110631Z&X-Amz-Expires=600&X-Amz-Signature=0ece900f77769fb3416ec2311504cab431b19e5e540827196969e4bd5d9ef790&X-Amz-SignedHeaders=host\",\n      \"id\": 48,\n      \"title\": \"Watch video to mangage tiredness\",\n      \"description\": \"Watch video to mangage tiredness & better sleep\",\n      \"createdBy\": \"b79c9272-48a8-4029-b133-983fe01ce578\",\n      \"createdAt\": \"2022-05-11T10:42:08.122Z\",\n      \"updatedAt\": \"2022-05-11T10:43:30.490Z\",\n      \"isPremium\": true,\n      \"views\": 25,\n      \"isPlayed\": true,\n      \"hasPermissions\": true,\n      \"length\": \"360\",\n      \"allowRating\": true,\n      \"language\": \"en\",\n      \"keywords\": [\n        \"Tired\",\n        \"Workout\",\n        \"Walking\",\n        \"Tiredness\"\n      ]\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "4a9897bc-f6d4-48ea-89a0-cd6b00ab30c4", "documentation": "Get all the schedules by self, we can use user_guid, schedule_id and start _date and end end_date as query param", "method": "get", "endpoint": "api/v1/schedules", "responses": [{"uuid": "2c478246-a47e-494d-a922-73abb1d0c1cc", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"count\":30,\n  \"data\": [\n    {\n      \"id\": 476513,\n      \"createdBy\": \"3b1245e3-650f-44a0-9af2\",\n      \"description\": \"High glucose spikes\",\n      \"start\": \"2022-05-31T15:15:00.000Z\",\n      \"end\": \"2022-05-31T15:30:00.000Z\",\n      \"scheduledBy\": \"3b1245e3-650f-44a0-9af2\",\n      \"scheduledWith\": [\n        \"2342skf-jwisf-nkf2-9341\"\n      ],\n      \"type\": \"appointment\",\n      \"isComplete\": false,\n      \"createdAt\": \"2022-05-31T15:15:28.995Z\",\n      \"sessionId\": \"12312-12313\",\n      \"formId\": 5\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "8a034791-da42-4b44-9996-d1886a1e9c01", "documentation": "Get all the settings for an user", "method": "get", "endpoint": "api/v1/settings", "responses": [{"uuid": "536026d9-a928-4110-b60a-116c74630ac4", "body": "{\n  \"success\":true,\n  \"message\":\"\",\n  \"data\": [\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"Appointment related things can be editable here\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Appontment Requests\",\n      \"settingName\": \"appt_requests\",\n      \"settingSubgroup\": \"Appointments\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Appontment Start Time\",\n      \"settingName\": \"appt_start_time\",\n      \"settingSubgroup\": \"Appointments\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"2:30 AM\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Appontment Start Time\",\n      \"settingName\": \"appt_end_time\",\n      \"settingSubgroup\": \"Appointments\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"4:00 PM\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Appontment Start Time\",\n      \"settingName\": \"appt_length\",\n      \"settingSubgroup\": \"Appointments\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"30\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Secure Message\",\n      \"settingName\": \"secure_message\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Credit Card Required for Appointment\",\n      \"settingName\": \"cc_payment_accepted\",\n      \"settingSubgroup\": \"Appointment\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Orders\",\n      \"settingName\": \"orders_enabled\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Visits CC Enabled\",\n      \"settingName\": \"mirth_ccd_enabled\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": false\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Cron Expression\",\n      \"settingName\": \"cron_expression\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"debug\",\n      \"settingName\": \"debug\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": \"\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Connection Requests\",\n      \"settingName\": \"connection_requests\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Release Medical\",\n      \"settingName\": \"release_medical\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": false\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Zip Code\",\n      \"settingName\": \"zip_code\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Record Encounter\",\n      \"settingName\": \"recording_enabled\",\n      \"settingSubgroup\": \"Encounters\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Transcribe Encounter\",\n      \"settingName\": \"transcription_enabled\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },{\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Signature\",\n      \"settingName\": \"singnature\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"Sign url\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Privacy\",\n      \"settingName\": \"privacy\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"boolean\",\n      \"settingValue\": true\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"Payments\",\n      \"settingLabel\": \"CC Status\",\n      \"settingName\": \"cc_status\",\n      \"settingSubgroup\": \"Preferences\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"not_captured\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": true,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"OAuth Status\",\n      \"settingName\": \"oauth_status\",\n      \"settingSubgroup\": \"Payments\",\n      \"settingType\": \"string\",\n      \"settingValue\": \"not_captured\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"CC Status\",\n      \"settingName\": \"cc_status\",\n      \"settingSubgroup\": \"Payments\",\n      \"settingType\": \"sting\",\n      \"settingValue\": \"not_captured\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Expiry\",\n      \"settingName\": \"expiry\",\n      \"settingSubgroup\": \"Payments\",\n      \"settingType\": \"sting\",\n      \"settingValue\": \"2022-07-11T10:42:08.122Z\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Premium_Type\",\n      \"settingName\": \"premium_type\",\n      \"settingSubgroup\": \"Payments\",\n      \"settingType\": \"sting\",\n      \"settingValue\": \"monthly\"\n    },\n    {\n      \"settingChoices\": null,\n      \"settingDescription\": \"\",\n      \"settingEditable\": false,\n      \"settingGroup\": \"General\",\n      \"settingLabel\": \"Renewal Date\",\n      \"settingName\": \"renewal_date\",\n      \"settingSubgroup\": \"Payments\",\n      \"settingType\": \"sting\",\n      \"settingValue\": \"2022-07-11T10:42:08.122Z\"\n    }\n    \n  ]\n}\n", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "f16a7995-1b90-4679-9241-48e5b7f2f044", "documentation": "", "method": "get", "endpoint": "api/v1/feed", "responses": [{"uuid": "4f1e3017-815c-4979-9b51-90dca8f18b16", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "cc9e7572-afa8-4523-915c-e34a51186e90", "documentation": "Get all the connected coaches, we need to support paginate and speciality based search to this API", "method": "get", "endpoint": "api/v1/coaches", "responses": [{"uuid": "9308bdfb-81da-4266-8680-d5412b060ff2", "body": "{\n  \"success\": true,\n  \"message\": \"\",\n  \"count\":10,\n  \"data\": [\n    {\n      \"group_name\": \"Others\",\n      \"coaches\": [\n        {\n          \"userGuid\": \"f85621e7-5399-4b78-a3a9-d2688f205c43\",\n          \"firstName\": \"<PERSON><PERSON><PERSON>\",\n          \"lastName\": \"<PERSON><PERSON>\",\n          \"name\": \"<PERSON><PERSON><PERSON>\",\n          \"status\": \"OFFLINE\",\n          \"avatar\": null,\n          \"gender\": \"male\",\n          \"role\": \"BUSER\",\n          \"favorite\": false,\n          \"title\": \"MD\",\n          \"specialty\": \"Fitness Coach\",\n          \"medicalAssistants\": [],\n          \"doctors\": []\n        },\n        {\n          \"userGuid\": \"b5677a72-87b9-4f72-934b-2e5eaecf347d\",\n          \"firstName\": \"<PERSON>\",\n          \"lastName\": \"Test\",\n          \"name\": \"Helen Test\",\n          \"status\": \"AVAILABLE\",\n          \"avatar\": null,\n          \"dob\": null,\n          \"gender\": \"female\",\n          \"role\": \"BUSER\",\n          \"favorite\": false,\n          \"title\": \"MBBS\",\n          \"specialty\": \"Obstetrics & Gynecology\",\n          \"medicalAssistants\": [],\n          \"doctors\": []\n        }\n      ]\n    }\n  ],\n  \"next\": null,\n  \"previous\": null\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}, {"uuid": "bc2f1ff8-464c-4986-9d37-b097b3661d88", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false}], "enabled": true, "responseMode": null}, {"uuid": "aec9f76b-534a-4098-9e9e-e5de8484dadd", "documentation": "", "method": "get", "endpoint": "api/v1/studio", "responses": [{"uuid": "0e3db20d-20bd-4111-a859-e57f700df860", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "32d575f4-20cf-4a37-9872-952b27dcc1dd", "documentation": "Get all the unconnected users by type in pagianted support response ", "method": "get", "endpoint": "api/v1/doctors", "responses": [{"uuid": "b9120942-f359-4588-af8e-05373fab892e", "body": "{\n  \"success\": true,\n  \"count\": 2,\n  \"prev\": null,\n  \"next\": \"/users?paginate=true&connection_requests=true&page=2&page_size=20&role=BUSER&sort_by=status&status_in=AVAILABLE,BUSY,AWAY,OFFLINE\",\n  \"data\": [\n    {\n      \"firstName\": \"<PERSON>thal<PERSON>\",\n      \"lastName\": \"Test\",\n      \"avatar\": \"https://india-tech-dev.s3.ap-southeast-1.amazonaws.com/images/users/1093/5a3201dc-4b12-4ad5-b151-d13afa8fbec0.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASXZ3C6L4DLRVRIOJ%2F20220610%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20220610T121429Z&X-Amz-Expires=3600&X-Amz-Signature=3dd639e9ad842f5f037e4423d7afb8f461905c8ef43e039b94f79be7dfa1718e&X-Amz-SignedHeaders=host\",\n      \"status\": \"AVAILABLE\",\n      \"userGuid\": \"b79c9272-48a8-4029-b133-983fe01ce578\",\n      \"isCorporate\": true,\n      \"tenant\": \"cognizant\",\n      \"role\": \"BUSER\",\n      \"gender\": \"male\",\n      \"specialty\": \"Pediatrics\",\n      \"title\": \"MBBS\"\n    },\n    {\n      \"firstName\": \"Kavin Sethu\",\n      \"lastName\": \"Doc  Heaila 1\",\n      \"avatar\": \"https://india-tech-dev.s3.ap-southeast-1.amazonaws.com/images/users/35/5724f2dd-8195-4a65-8a60-b3fdb3067fdb.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASXZ3C6L4DLRVRIOJ%2F20220610%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20220610T121429Z&X-Amz-Expires=3600&X-Amz-Signature=afabd12b0eb0bb5b92ec28c2e5159a537ecfa48f4039180c45027e5918056cc1&X-Amz-SignedHeaders=host\",\n      \"dob\": null,\n      \"name\": \"Kavin Sethu Doc  Heaila 1\",\n      \"status\": \"AVAILABLE\",\n      \"email\": \"<EMAIL>\",\n      \"userGuid\": \"b7183e62-f031-49f8-878c-d1a619ed0af4\",\n      \"isCorporate\": true,\n      \"tenant\": \"Tata Consultancy Service\",\n      \"role\": \"BUSER\",\n      \"specialty\": \"Internal Medicine\",\n      \"title\": \"MD MS\"\n    }\n  ]\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "0dcc99d4-e5e7-4104-9b7b-dd1fb1afda80", "documentation": "get the first form from the server ", "method": "get", "endpoint": "api/v1/forms/0", "responses": [{"uuid": "b51bec8a-6583-4e6e-ad7d-6a9f3307a4bf", "body": "{\n  \"success\":true\n  \"message\":\"forms.json\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "742ae1ca-f05f-4df6-b465-c2ee3755e0f2", "documentation": "", "method": "post", "endpoint": "api/v1/registration", "responses": [{"uuid": "0f2272f5-053c-4f9a-8b9a-e4924fdc379b", "body": "{\n  \"success\":true,\n  \"message\":\"User Created Successfully\",\n  \"token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.83YwxT9AQlQugBGZoiw9o2iVcwLdFeYNafFV-CvTJ50\",\n  \"userGuid\":\"b79c9272-48a8-4029-b133-983fe01ce578\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "user_data", "value": "{\"phone\": \"+15302825260\",   \"password\": \"Health@9876\",   \"email\": \"<EMAIL>\",   \"role\": \"USER\",   \"is_corporate_user\": true,   \"phone_udid\":\"\" }", "operator": "equals", "invert": false}], "rulesOperator": "AND", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "d7a6499b-3079-4584-8d90-14c22f9253e4", "documentation": "sending one time password to the user after register", "method": "post", "endpoint": "api/v1/otp", "responses": [{"uuid": "6076555c-48f3-4b7c-8cd5-da0f007314ba", "body": "{\n  \"success\":true,\n  \"message\":\"otp sent successfully\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "", "value": "{ \"user_guid\":\"c5a880ac-b6b5-4b98-9280-7b39b9827e71\"}", "operator": "equals", "invert": false}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "a2d0b30d-72b5-4570-9ae1-e053c1d8ea8f", "documentation": "", "method": "post", "endpoint": "api/v1/otp/validate", "responses": [{"uuid": "03aea35e-59a2-4078-a833-62d318ed3b1b", "body": "{\n  \"success\":true,\n  \"message\":\"validated successfully\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "", "value": "{ \t\"user_guid\": \"ebd80307-ac12-40ae-89da-62786ff7807d\", \t\"otp\": \"654123\" }", "operator": "equals", "invert": false}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "c2e33ba3-241a-40de-ac74-6e6709fe3733", "documentation": "Update user profile with valid data", "method": "put", "endpoint": "api/v1/users/profile", "responses": [{"uuid": "c5806107-a186-45bc-a348-96e21f2a253c", "body": "{\n  \"success\":true,\n  \"message\":\"user profile updated successfully\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "profile_complete", "value": "true", "operator": "equals", "invert": false}, {"target": "body", "modifier": "user_data", "value": "{\"phone\": \"+15302825260\",\"password\" : \"Health@9876\", \"email\" : \"<EMAIL>\", \"role\" : \"USER\", \"is_corporate_user\":true}", "operator": "equals", "invert": false}, {"target": "body", "modifier": "user_detail", "value": "{\"age\":\"24\",\"gender\":\"male\",\"height\":\"179\",\"weight\":\"69\",\"first_name\":\"<PERSON>\",\"last_name\":\"phillip\"}", "operator": "equals", "invert": false}], "rulesOperator": "AND", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "30ae1373-39a9-4ae6-a5c0-497973c64035", "documentation": "To login a user", "method": "post", "endpoint": "api/v1/ogin", "responses": [{"uuid": "b40475dc-0c02-4ab6-9c76-78748e260a85", "body": "{\n    \"success\": true,\n    \"message\": \"Token active.\",\n    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.83YwxT9AQlQugBGZoiw9o2iVcwLdFeYNafFV-CvTJ50\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "", "value": "{\"email\":\"<EMAIL>\",\"password\":\"Vivek_21\",\"is_corporate_user\":true,\"phone_udid\":\"\"}", "operator": "equals", "invert": false}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "d1139179-5c5b-4554-94e6-ac0b51903ad8", "documentation": "To reset password", "method": "put", "endpoint": "v1/reset-password", "responses": [{"uuid": "5f9b2caf-9a8b-4f94-bf5c-da03ef8e2239", "body": "{\n  \"success\": true,\n  \"message\": \"password update successfully\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "", "value": "{\"user_guid\":\"b79c9272-48a8-4029-b133-983fe01ce578\",\"confirm_password\":\"Vivek_21\",\"otp\":\"654123\",\"password\":\"Vivek_21\",\"app_type\":\"patient\",\"is_corporate_user\":true}", "operator": "equals", "invert": false}], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "60b8f2c9-631d-41ea-9d70-84d2991bb896", "documentation": "Get all the Notifications with paginate and filter support", "method": "get", "endpoint": "api/v1/notifications", "responses": [{"uuid": "b0011699-c605-4785-a999-aff53976c125", "body": "{\n  \"success\": true,\n  \"next\": null,\n  \"previous\": null,\n  \"count\": 13,\n  \"unreadCount\": 5,\n  \"data\": [\n    {\n      \"requestId\": 5159,\n      \"type\": \"comorbidities\",\n      \"message\": \"Mithali Test has added a comorbidity for Cancer\",\n      \"status\": \"open\",\n      \"entityId\": 226,\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/notifications-icons/connection.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=9v%2B4ZzwLLJJ2Ia8iM1JgmctV15k%3D\",\n      \"isSeen\": true,\n      \"createdAt\": \"2022-06-02T08:06:13.411Z\",\n      \"updatedAt\": \"2022-06-07T06:33:29.605Z\",\n      \"timeStamp\": \"**********\",\n      \"detail\": {},\n      \"requestor\": \"b79c9272-48a8-4029-b133-983fe01ce578\"\n    },\n    {\n      \"requestId\": 5501,\n      \"type\": \"appointment\",\n      \"message\": \"Runny nose\",\n      \"status\": \"open\",\n      \"createdAt\": \"2022-06-02T08:06:13.411Z\",\n      \"updatedAt\": \"2022-06-07T06:33:29.605Z\",\n      \"timeStamp\": \"**********\",\n      \"isSeen\": true,\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/notifications-icons/appointment.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=calodhWP2FixrW8Sc9IV8NapxK0%3D\",\n      \"entityId\": null,\n      \"detail\": {\n        \"dates\": [\n          {\n            \"start\": \"2022-06-17T12:30:00.000Z\",\n            \"end\": \"2022-06-17T12:50:00.000Z\"\n          },\n          {\n            \"start\": \"2022-06-17T12:10:00.000Z\",\n            \"end\": \"2022-06-17T12:30:00.000Z\"\n          },\n          {\n            \"start\": \"2022-06-17T11:50:00.000Z\",\n            \"end\": \"2022-06-17T12:10:00.000Z\"\n          }\n        ]\n      },\n      \"requestor\": \"b5677a72-87b9-4f72-934b-2e5eaecf347d\"\n    }\n  ]\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "8f09bd7e-0d59-4f33-a4b8-31452652167b", "documentation": "", "method": "get", "endpoint": "api/v1/messages", "responses": [{"uuid": "7a36d936-1218-488d-9026-6d66c9bc9598", "body": "  {\n    \"count\" : 1,\n    \"prev\" : null,\n    \"next\" : null,\n    \"success\" : true,\n    \"message\":\"\",\n    \"data\" : [\n      {\n        \"userGuid\" : \"0b957845-fbc4-471b-b73c-d74d25fb6304\"\n        \"message\" : \"MwgAEiEFGBjD9hdEhHrlLh1Kfo6kYn7Nw4cCA136KOgJkmULAR0aIQWT34rWDfDeeI7x6yZv5Jc\\/cynASevzezUbhmcAsUzUZSJCMwohBf04mui1RaTM8w7SfzqoQBlxYEYpXO57rtF60Pl5xCYpEAAYACIQDOMWBSsXW6GdZiqbfb1p7HDlIxQzNSl2KODbrZsHMAA=\",\n        \"fileType\" : null,\n        \"createdAt\" : \"2022-06-17T14:49:47.821Z\"\n      }\n    ]\n  }", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "46e4cfde-a1ef-4104-ad73-5363293778b3", "documentation": "", "method": "get", "endpoint": "api/v1/wallet", "responses": [{"uuid": "39314091-b6a3-4b48-bf91-7c8ef013a79c", "body": "{\n  \"success\": true,\n  \"message\":true\n  \"data\": [\n    {\n      \"id\":1,\n      \"type\": \"comorbidities\",\n      \"count\": 2,\n      \"disaplayName\": \"Comorbidities\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"platform\": 2\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/comorbidities.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=9sDBiraajtbkX3Suk8nlq8ZbPFw%3D\"\n    },\n    {\n       \"id\":2,\n      \"type\": \"symptom-checker\",\n      \"count\": 0,\n      \"disaplayName\": \"Symptom Checker\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/symptom-checker.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=ZGoUzo9LPK8aNOofsZLJBD5yIR8%3D\"\n    },\n    {\n      \"id\":3,\n      \"type\": \"investigations\",\n      \"count\": 0,\n      \"disaplayName\": \"Investigations\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/investigations.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=rB7lFimjdQPSm8dvjAikkh6KXa0%3D\"\n    },\n    {\n       \"id\":4,\n      \"type\": \"allergies\",\n      \"count\": 3,\n      \"disaplayName\": \"Allergies\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 3\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/allergies.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=sgTny2mBSOsBHyj3HEH1jI8OuVc%3D\"\n    },\n    {\n       \"id\":5,\n      \"type\": \"care_plan\",\n      \"count\": 0,\n      \"disaplayName\": \"Plan of Care\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/care_plan.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=rMqpcAEaGS5%2F9vo8TfjPLM3r8Kk%3D\"\n    },\n    {\n       \"id\":6,\n      \"type\": \"documents\",\n      \"count\": 1,\n      \"disaplayName\": \"Documents\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"health-gorilla\": 0,\n        \"platform\": 1\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/documents.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=p6gj%2FYAWaNgtdD%2FlMdIKNAUGzOo%3D\"\n    },\n    {\n       \"id\":6,\n      \"type\": \"procedures\",\n      \"count\": 0,\n      \"disaplayName\": \"Procedures\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/procedures.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=jkkRhAsM5EWIxOod4DLqAeelLUc%3D\"\n    },\n    {\n       \"id\":7,\n      \"type\": \"diagnoses\",\n      \"count\": 0,\n      \"disaplayName\": \"Diagnoses\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"health-gorilla\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/diagnoses.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=QeCsvxgyc2eej7443vWcbNeFYYk%3D\"\n    },\n    {\n       \"id\":8,\n      \"type\": \"immunizations\",\n      \"count\": 0,\n      \"disaplayName\": \"Immunizations\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/immunizations.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=2gTj6Zsyqb1HcogME0XzbcFOhfs%3D\"\n    },\n    {\n       \"id\":9,\n      \"type\": \"social_history\",\n      \"count\": 0,\n      \"disaplayName\": \"Social History\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/social_history.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=TY8tJTIS6ADFlCyfcWybAfWQaJE%3D\"\n    },\n    {\n       \"id\":10,\n      \"type\": \"family_history\",\n      \"count\": 0,\n      \"disaplayName\": \"Family History\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/family_history.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=PNrIN49sNL%2BeHPzez5RCfed%2FJPY%3D\"\n    },\n    {\n       \"id\":11,\n      \"type\": \"medications\",\n      \"count\": 1,\n      \"disaplayName\": \"Medications\",\n      \"canCreate\": true,\n      \"detail\": {\n        \"health-gorilla\": 0,\n        \"mms\": 0,\n        \"platform\": 1\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/medications.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=KLd8LUY%2Ba55MmjybbtRzDBN0nLI%3D\"\n    },\n    {\n       \"id\":12,\n      \"type\": \"results\",\n      \"count\": 1,\n      \"disaplayName\": \"Labs\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"health-gorilla\": 0,\n        \"platform\": 1\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/results.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=x%2FiTaM8%2FPa6kd87yMa5ZbFR33Rw%3D\"\n    },\n    {\n       \"id\":13,\n      \"type\": \"x-ray\",\n      \"count\": 0,\n      \"disaplayName\": \"Radiology\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/x-ray.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=ZLS0JtSRw%2BN3dwAU9ZxTygSMttk%3D\"\n    },\n    {\n       \"id\":14,\n      \"type\": \"specialist\",\n      \"count\": 0,\n      \"disaplayName\": \"Specialists\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/specialist.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=2NXdYrwLS0kCKVM3fE9tLoYteUQ%3D\"\n    },\n    {\n       \"id\":15,\n      \"type\": \"miscellaneous\",\n      \"count\": 0,\n      \"disaplayName\": \"Miscellaneous\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/miscellaneous.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=E724LPB0Z%2BvKZrATsffFLfJfpPo%3D\"\n    },\n    {\n       \"id\":16,\n      \"type\": \"educational-videos\",\n      \"count\": 0,\n      \"disaplayName\": \"Educational Videos\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/educational-videos.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=85o4l08KWBvYG%2BUpvdheoBY1Big%3D\"\n    },\n    {\n       \"id\":17,\n      \"type\": \"forms\",\n      \"count\": 0,\n      \"disaplayName\": \"Forms\",\n      \"canCreate\": false,\n      \"detail\": {\n        \"platform\": 0\n      },\n      \"imageUrl\": \"https://healiahealthcare-provider.s3.ap-southeast-1.amazonaws.com/health-summaries-icons/forms.png?AWSAccessKeyId=AKIASXZ3C6L4DLRVRIOJ&Expires=**********&Signature=nIH4GZm1zMIsedpkKAmTAyMalok%3D\"\n    }\n  ]\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}], "enabled": true, "responseMode": null}, {"uuid": "1cf46be0-4a70-4d09-a1cf-5aef5e5ddcd0", "documentation": "The query params will be used like complete=true, complete=false ", "method": "get", "endpoint": "api/v1/health-summary/:type", "responses": [{"uuid": "6382d7a0-5af3-4c56-a0c0-5d15b4c5e906", "body": "{\n  \"success\": true,\n  \"count\": 2,\n  \"prev\": null,\n  \"next\": null,\n  \"data\": [\n    {\n      \"userComorbidityId\": 226,\n      \"id\": 5,\n      \"name\": \"Cancer\",\n      \"data\": [\n        {\n          \"items\": [\n            {\n              \"value\": \"No\",\n              \"properties\": {\n                \"maxFile\": null,\n                \"options\": [\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"Yes\",\n                    \"score\": null,\n                    \"nextQuestionId\": 2,\n                    \"multiSelectGroup\": null\n                  },\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"No\",\n                    \"score\": null,\n                    \"nextQuestionId\": null,\n                    \"multiSelectGroup\": null\n                  }\n                ],\n                \"min\": null,\n                \"type\": \"list\",\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"onlyNumbers\": null,\n                \"skipNextQuestionId\": null,\n                \"max\": null,\n                \"uom\": null,\n                \"dataType\": \"text\"\n              },\n              \"score\": null,\n              \"question\": \"Do you have Cancer?\",\n              \"questionId\": 1,\n              \"field\": null\n            },\n            {\n              \"value\": null,\n              \"properties\": {\n                \"maxFile\": null,\n                \"options\": [\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"Year\",\n                    \"score\": null,\n                    \"nextQuestionId\": 3,\n                    \"multiSelectGroup\": null\n                  },\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"Month\",\n                    \"score\": null,\n                    \"nextQuestionId\": 4,\n                    \"multiSelectGroup\": null\n                  },\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"Week\",\n                    \"score\": null,\n                    \"nextQuestionId\": 5,\n                    \"multiSelectGroup\": null\n                  },\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"Day(s)\",\n                    \"score\": null,\n                    \"nextQuestionId\": 6,\n                    \"multiSelectGroup\": null\n                  }\n                ],\n                \"min\": null,\n                \"type\": \"list\",\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"onlyNumbers\": null,\n                \"skipNextQuestionId\": null,\n                \"max\": null,\n                \"uom\": null,\n                \"dataType\": \"text\"\n              },\n              \"score\": null,\n              \"question\": \"What is the duration of your Cancer?\",\n              \"questionId\": 2,\n              \"field\": null\n            },\n            {\n              \"value\": null,\n              \"properties\": {\n                \"maxFile\": null,\n                \"options\": [\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"\",\n                    \"score\": null,\n                    \"nextQuestionId\": null,\n                    \"multiSelectGroup\": null\n                  }\n                ],\n                \"min\": null,\n                \"type\": \"numeric\",\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"onlyNumbers\": null,\n                \"skipNextQuestionId\": null,\n                \"max\": null,\n                \"uom\": null,\n                \"dataType\": \"text\"\n              },\n              \"score\": null,\n              \"question\": \"How many Years?\",\n              \"questionId\": 3,\n              \"field\": null\n            },\n            {\n              \"value\": null,\n              \"properties\": {\n                \"maxFile\": null,\n                \"options\": [\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"\",\n                    \"score\": null,\n                    \"nextQuestionId\": null,\n                    \"multiSelectGroup\": null\n                  }\n                ],\n                \"min\": null,\n                \"type\": \"numeric\",\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"onlyNumbers\": null,\n                \"skipNextQuestionId\": null,\n                \"max\": null,\n                \"uom\": null,\n                \"dataType\": \"text\"\n              },\n              \"score\": null,\n              \"question\": \"How many Months?\",\n              \"questionId\": 4,\n              \"field\": null\n            },\n            {\n              \"value\": null,\n              \"properties\": {\n                \"maxFile\": null,\n                \"options\": [\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"\",\n                    \"score\": null,\n                    \"nextQuestionId\": null,\n                    \"multiSelectGroup\": null\n                  }\n                ],\n                \"min\": null,\n                \"type\": \"numeric\",\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"onlyNumbers\": null,\n                \"skipNextQuestionId\": null,\n                \"max\": null,\n                \"uom\": null,\n                \"dataType\": \"text\"\n              },\n              \"score\": null,\n              \"question\": \"How many Weeks?\",\n              \"questionId\": 5,\n              \"field\": null\n            },\n            {\n              \"value\": null,\n              \"properties\": {\n                \"maxFile\": null,\n                \"options\": [\n                  {\n                    \"isOtherOption\": false,\n                    \"value\": \"\",\n                    \"score\": null,\n                    \"nextQuestionId\": null,\n                    \"multiSelectGroup\": null\n                  }\n                ],\n                \"min\": null,\n                \"type\": \"numeric\",\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"onlyNumbers\": null,\n                \"skipNextQuestionId\": null,\n                \"max\": null,\n                \"uom\": null,\n                \"dataType\": \"text\"\n              },\n              \"score\": null,\n              \"question\": \"How many Days?\",\n              \"questionId\": 6,\n              \"field\": null\n            }\n          ],\n          \"title\": \"Header\"\n        }\n      ],\n      \"status\": \"completed\",\n      \"userGuid\": \"b79c9272-48a8-4029-b133-983fe01ce578\",\n      \"updatedBy\": \"b79c9272-48a8-4029-b133-983fe01ce578\",\n      \"canEdit\": true,\n      \"canDelete\": true,\n      \"createdAt\": \"2022-06-02T08:06:13.395Z\",\n      \"updatedAt\": \"2022-06-02T08:06:13.395Z\"\n    },\n    {\n      \"userComorbidityId\": 201,\n      \"id\": 18,\n      \"name\": \"Ulcer or Stomach Disease\",\n      \"data\": [\n        {\n          \"items\": [\n            {\n              \"properties\": {\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"options\": [\n                  {\n                    \"nextQuestionId\": 2,\n                    \"score\": 0,\n                    \"value\": \"Yes\"\n                  },\n                  {\n                    \"score\": 0,\n                    \"value\": \"No\"\n                  }\n                ],\n                \"type\": \"list\"\n              },\n              \"question\": \"Do you have Ulcer or Stomach Disease?\",\n              \"questionId\": 1,\n              \"score\": 0,\n              \"value\": \"Yes\"\n            },\n            {\n              \"properties\": {\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"options\": [\n                  {\n                    \"nextQuestionId\": 3,\n                    \"score\": 0,\n                    \"value\": \"Year\"\n                  },\n                  {\n                    \"nextQuestionId\": 4,\n                    \"score\": 0,\n                    \"value\": \"Month\"\n                  },\n                  {\n                    \"nextQuestionId\": 5,\n                    \"score\": 0,\n                    \"value\": \"Week\"\n                  },\n                  {\n                    \"nextQuestionId\": 6,\n                    \"score\": 0,\n                    \"value\": \"Day(s)\"\n                  }\n                ],\n                \"type\": \"list\"\n              },\n              \"question\": \"What is the duration of your Ulcer or Stomach Disease?\",\n              \"questionId\": 2,\n              \"score\": 0,\n              \"value\": \"Month\"\n            },\n            {\n              \"properties\": {\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"options\": [\n                  {\n                    \"score\": 0,\n                    \"value\": \"\"\n                  }\n                ],\n                \"type\": \"numeric\"\n              },\n              \"question\": \"How many Years?\",\n              \"questionId\": 3\n            },\n            {\n              \"properties\": {\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"options\": [\n                  {\n                    \"score\": 0,\n                    \"value\": \"\"\n                  }\n                ],\n                \"type\": \"numeric\"\n              },\n              \"question\": \"How many Months?\",\n              \"questionId\": 4,\n              \"value\": \"4\"\n            },\n            {\n              \"properties\": {\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"options\": [\n                  {\n                    \"score\": 0,\n                    \"value\": \"\"\n                  }\n                ],\n                \"type\": \"numeric\"\n              },\n              \"question\": \"How many Weeks?\",\n              \"questionId\": 5,\n              \"value\": \"\"\n            },\n            {\n              \"properties\": {\n                \"allowMultipleSelection\": false,\n                \"isRequired\": true,\n                \"options\": [\n                  {\n                    \"score\": 0,\n                    \"value\": \"\"\n                  }\n                ],\n                \"type\": \"numeric\"\n              },\n              \"question\": \"How many Days?\",\n              \"questionId\": 6,\n              \"value\": \"\"\n            }\n          ],\n          \"title\": \"Header\",\n          \"titleNeedToBeDisplay\": false\n        }\n      ],\n      \"status\": \"completed\",\n      \"userGuid\": \"b79c9272-48a8-4029-b133-983fe01ce578\",\n      \"updatedBy\": \"b79c9272-48a8-4029-b133-983fe01ce578\",\n      \"canEdit\": true,\n      \"canDelete\": true,\n      \"createdAt\": \"2022-05-11T13:58:34.445Z\",\n      \"updatedAt\": \"2022-05-11T13:58:34.445Z\"\n    }\n  ]\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true}, {"uuid": "573aeee8-7c9e-4316-8568-bc9f91a96ce4", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false}], "enabled": true, "responseMode": null}], "proxyMode": false, "proxyHost": "", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Access-Token", "value": "{{token}}"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}]}
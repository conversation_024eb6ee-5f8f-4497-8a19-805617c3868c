const jwt = require('jsonwebtoken');
const { log: logger } = require("../utils/logger");

module.exports.jwtValidator = function (req, res, next) {
    var accessTokenKey = "X-Access-Token".toLowerCase();
    var apiHeaderKey = "X-Api-Key".toLowerCase();
    var token = req.headers[accessTokenKey];
    var apiKey = req.headers[apiHeaderKey];

    logger.debug({
        hasApiKey: !!apiKey,
        hasToken: !!token,
        tokenValue: token === 'null' ? 'string-null' : (token ? 'valid-token' : 'no-token'),
        url: req.url
    }, 'JWT validation attempt');

    if (apiKey) {
        logger.debug('API key authentication successful');
        next();
    } else if (token && token !== 'null' && token !== null) {
        try {
            const decoded = jwt.decode(token, { complete: true });
            if (decoded && decoded.payload) {
                req.decoded = decoded.payload;
                logger.debug('JWT token authentication successful');
                next();
            } else {
                logger.warn({ token: token.substring(0, 20) + '...' }, 'Invalid JWT token - decode failed');
                return res.status(401).send({
                    success: false,
                    message: 'Invalid token provided.'
                });
            }
        } catch (error) {
            logger.error({
                error: error.message,
                token: token.substring(0, 20) + '...'
            }, 'JWT decode error');
            return res.status(401).send({
                success: false,
                message: 'Invalid token provided.'
            });
        }
    } else {
        logger.warn({
            token: token,
            url: req.url
        }, 'No valid token provided');
        return res.status(403).send({
            success: false,
            message: 'No token provided.'
        });
    }
};
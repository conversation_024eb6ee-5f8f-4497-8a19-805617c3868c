const { roundOffNumber, getUTCDateTimeByLocalDate } = require("../../utils/helpers");
const { getStaticSources, getStaticDevices } = require("../../utils/staticData");

async function getLogMetadata(userId) {
  const now = new Date().toISOString();
  const sources = await getStaticSources();
  const devices = await getStaticDevices();
  const sourceId = sources.Fitbit.id;
  const sourceName = sources.Fitbit.name;
  const deviceId = devices.filter(x => x.sourceId == sourceId)[0]?.id || 11;
  const deviceName = devices.filter(x => x.id == deviceId)[0]?.name || 'Fitbit device';
  return {
    userId, createdAt: now, updatedAt: now,
    sourceId, sourceName, deviceId, deviceName
  }
}

async function mapWaterByDate(userId, date, waterData, offsetFromUTCMillis) {
  if (!waterData || Object.keys(waterData)?.length === 0)
    return {};
  const metaData = await getLogMetadata(userId);
  const timestamp = new Date(date)
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  return {
    logId: waterData.logId || null,
    timestamp: timestamp.toISOString(), 
    value: waterData.summary?.water || 0, // This may come as zero from fitbit itself
    unit: "ml",
    ...metaData
  };
}

async function mapActivitySummary(userId, date, activitySummary, offsetFromUTCMillis) {
  if (!activitySummary || Object.keys(activitySummary)?.length === 0)
    return {};
  const timestamp = new Date(date)
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: activitySummary.logId || null,
    timestamp: timestamp.toISOString(),
    steps: activitySummary.steps,
    elevation: activitySummary?.elevation || 0,
    floors: activitySummary?.floors || 0,
    distance: activitySummary?.distances[0].distance || 0,
    activityCalories: {
      bmrCalories: activitySummary?.caloriesBMR || 0,
      activeCalories: activitySummary?.activityCalories || 0,
      totalCalories:
        (activitySummary?.caloriesBMR || 0) +
        (activitySummary?.activityCalories || 0),
    },
    activityTime: {
      lightlyActiveSeconds: (activitySummary?.lightlyActiveMinutes || 0) * 60,
      fairlyActiveSeconds: (activitySummary?.fairlyActiveMinutes || 0) * 60,
      veryActiveSeconds: (activitySummary?.veryActiveMinutes || 0) * 60,
      totalActiveSeconds: ((activitySummary?.lightlyActiveMinutes || 0) + (activitySummary?.fairlyActiveMinutes || 0) + (activitySummary?.veryActiveMinutes || 0)) * 60,
    },
    unit: "kcal",
    ...metaData
  };
}

async function mapActivityLog(userId, fitbitLog, offsetFromUTCMillis) {
  if (!fitbitLog || Object.keys(fitbitLog)?.length === 0)
    return {};

  const date = new Date(fitbitLog?.startTime?.split(".")[0])
  date.setTime(date.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: fitbitLog.logId || null,
    timestamp: date.toISOString(),
    activityName: fitbitLog?.activityName,
    activityTypeId: fitbitLog?.activityTypeId,
    duration: (fitbitLog?.duration) / 1000, // milisecond to second conversion
    activeDuration: (fitbitLog?.activeDuration) / 1000,
    calories: fitbitLog?.calories,
    steps: fitbitLog?.steps,
    distance: fitbitLog?.distance,
    unit: "km",
    elevationGain: fitbitLog?.elevationGain,
    ...metaData
  };
}

async function mapSleepLogs(userId, sleepLog, offsetFromUTCMillis) {
  if (!sleepLog || Object.keys(sleepLog)?.length === 0 || !sleepLog.dateOfSleep)
    return {};

  const startTime = new Date(sleepLog?.startTime?.split(".")[0])
  startTime.setTime(startTime.getTime() - offsetFromUTCMillis); 
  
  const endTime = new Date(sleepLog?.endTime?.split(".")[0])
  endTime.setTime(endTime.getTime() - offsetFromUTCMillis); 
  
  const dayOfSleepUTC = getUTCDateTimeByLocalDate(sleepLog.dateOfSleep, offsetFromUTCMillis / (60 * 1000)) 
  // Modifying the summary object 
  for (let key in sleepLog.levels.summary) {
    let obj = sleepLog.levels.summary[key];
    if (obj.minutes !== undefined) {
      obj.seconds = obj.minutes * 60;
      delete obj.minutes;
    }
    if (obj.thirtyDayAvgMinutes !== undefined) {
      obj.thirtyDayAvgSeconds = obj.thirtyDayAvgMinutes * 60;
      delete obj.thirtyDayAvgMinutes;
    }
  }
  const metaData = await getLogMetadata(userId);
  const doc = {
    logId: sleepLog.logId || null,
    startTime: startTime.toISOString(),
    endTime: endTime.toISOString(),
    timestamp: dayOfSleepUTC,
    duration: (sleepLog.duration) / 1000, // milisecond to second conversion
    efficiency: sleepLog.efficiency,
    timeInBed: (sleepLog.timeInBed) * 60, // min to sec conversion
    secondsToFallAsleep: sleepLog.minutesToFallAsleep * 60,
    secondsAsleep: sleepLog.minutesAsleep * 60,
    secondsAwake: sleepLog.minutesAwake * 60,
    secondsAfterWakeup: sleepLog.minutesAfterWakeup * 60,
    isMainSleep: sleepLog.isMainSleep,
    levels: {
      data: sleepLog.levels.data,
      summary: sleepLog.levels.summary,
    },
    unit: "sec",
    ...metaData
  };
  return doc;
}

async function mapECGLogs(userId, ecgLog, offsetFromUTCMillis) {
  if (!ecgLog || Object.keys(ecgLog)?.length === 0)
    return {};

  let {featureVersion, firmwareVersion, startTime, ...rest} = ecgLog

  let timestamp = new Date(startTime?.split(".")[0])
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: ecgLog.logId || null,
    ...rest, timestamp: timestamp.toISOString(),
    unit: "mm",
    ...metaData
  };
}

async function mapWeightByDate(userId, weightData, offsetFromUTCMillis) {
  if (!weightData || Object.keys(weightData)?.length === 0)
    return {};

  const timestamp = new Date(weightData.date);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: weightData.logId || null,
    timestamp: timestamp.toISOString(),
    value: weightData.weight,
    unit: "kg",
    ...metaData
  };
}

async function mapBMIByDate(userId, weightData, offsetFromUTCMillis) {
  if (!weightData || Object.keys(weightData)?.length === 0)
    return {};

  const timestamp = new Date(weightData.date);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: weightData.logId || null,
    timestamp: timestamp.toISOString(),
    value: weightData.bmi,
    unit: "kg/m2",
    ...metaData
  };
}

async function mapFatByDate(userId, fatData, offsetFromUTCMillis) {
  if (!fatData || Object.keys(fatData)?.length === 0)
    return {};

  const timestamp = new Date(fatData.date);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: fatData.logId || null,
    timestamp: timestamp.toISOString(),
    value: fatData.fat,
    unit: "%",
    ...metaData
  };
}

async function mapHeartRateByDate(userId, heartRateData, offsetFromUTCMillis) {
  let timestamp = new Date(heartRateData?.["activities-heart"]?.[0]?.dateTime);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  timestamp = timestamp.toISOString();

  if (!heartRateData || Object.keys(heartRateData)?.length === 0)
    return {};

  const heartRateZones = heartRateData?.["activities-heart"]?.[0]?.value?.heartRateZones?.map(
    (ele) => {
      const { name, minutes, ...rest } = ele
      return {zone: name, duration: minutes, ...rest}
    }
  ) || [];
  let intraday = heartRateData?.["activities-heart-intraday"]?.dataset || []
  intraday = intraday.map(entry => {
    const timeParts = entry.time?.split(':');
    let modifiedTimestamp = new Date(timestamp);
    modifiedTimestamp.setUTCHours(Number(timeParts?.[0] || 0), Number(timeParts?.[1] || 0), Number(timeParts?.[2] || 0));
    return { timestamp: modifiedTimestamp.toISOString(), value: entry?.value || 0 };
  });
  const metaData = await getLogMetadata(userId);
  return {
    logId: heartRateData.logId || null,
    unit: "bpm",
    timestamp,
    heartRateZones, intraday,
    ...metaData
  };
}

async function mapRestingHeartRateByDate(userId, heartRateData, offsetFromUTCMillis) {
  if (!heartRateData || Object.keys(heartRateData)?.length === 0)
    return {};
  
  const timestamp = new Date(heartRateData.dateTime);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: heartRateData.logId || null,
    timestamp: timestamp.toISOString(),
    value: heartRateData.value?.restingHeartRate || null, // Value may not be available
    unit: "bpm",
    ...metaData
  };
}

async function mapSpo2ByDate(userId, spo2Data, offsetFromUTCMillis) {
  if (!spo2Data || Object.keys(spo2Data)?.length === 0)
    return {};
  
  const timestamp = new Date(spo2Data.dateTime);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: spo2Data.logId || null,
    timestamp: timestamp.toISOString(),
    min: roundOffNumber(spo2Data.value.min, 0),
    max: roundOffNumber(spo2Data.value.max, 0),
    value: roundOffNumber(spo2Data.value.avg, 0),
    unit: "%",
    ...metaData
  };
}

async function mapVo2ByDate(userId, vo2Data, offsetFromUTCMillis) {
  if (!vo2Data || Object.keys(vo2Data)?.length === 0)
    return {};

  const timestamp = new Date(vo2Data.dateTime);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const values = vo2Data.value.vo2Max.split("-");
  let value = 0
  if (values?.length) {
    value = values?.length > 1 ? (Number(values[0]) + Number(values[1]))/2 : Number(values[0])
  } else return {};
  const metaData = await getLogMetadata(userId);
  return {
    logId: vo2Data.logId || null,
    timestamp: timestamp.toISOString(),
    value,
    unit: "mL/kg/min",
    ...metaData
  };
}

async function mapHRVByDate(userId, HRVData, offsetFromUTCMillis) {
  const data = HRVData.hrv?.[0];
  if (!data || data?.length == 0)
    return {};

  const timestamp = new Date(data.dateTime);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  return {
    logId: HRVData.logId || null,
    timestamp: timestamp.toISOString(),
    dailyRmssd: data.value.dailyRmssd,
    deepRmssd: data.value.deepRmssd,
    unit: "ms",
    ...metaData
  };
}

async function mapTempCore(userId, tempCore, offsetFromUTCMillis) {
  if (!tempCore || Object.keys(tempCore)?.length === 0)
    return {};
  const metaData = await getLogMetadata(userId);
  let { dateTime, value } = tempCore;
  let timestamp = new Date(dateTime?.split(".")[0])
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);  
  return {
    logId: tempCore.logId || null,
    timestamp: timestamp.toISOString(), value,
    unit: "°C",
    ...metaData
  };
}

const mapper = {
  mapActivityLog,
  mapActivitySummary,
  mapSleepLogs,
  mapECGLogs,
  mapWeightByDate,
  mapBMIByDate,
  mapFatByDate,
  mapHeartRateByDate,
  mapRestingHeartRateByDate,
  mapSpo2ByDate,
  mapVo2ByDate,
  mapHRVByDate,
  mapWaterByDate,
  mapTempCore
};

module.exports = { mapper };

const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log: logger } = require("../../utils/logger");
const { mapper } = require("./mappers");

async function getActivityList(userId, accessToken, afterDate, offsetFromUTCMillis) {
  const url = config.fitbit.api_base_url + "/1/user/-/activities/list.json";
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
    params: {
      afterDate,
      sort: "desc",
      limit: 100,
      offset: 0,
    },
  };
  try {
    const response = await axios(options);
    const fitbitLogs = response.data.activities;
    const mappedResponse = await Promise.all(fitbitLogs.map(async (fitbitLog) => {
      return await mapper.mapActivityLog(userId, fitbitLog, offsetFromUTCMillis);
    }));    
    return mappedResponse;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return [];
  }
}

async function getSleepList(userId, accessToken, afterDate, offsetFromUTCMillis) {
  var url = config.fitbit.api_base_url + `/1.2/user/-/sleep/list.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
    params: {
      afterDate,
      sort: "desc",
      limit: 100,
      offset: 0,
    },
  };
  try {
    const response = await axios(options);
    const sleepData = response.data;
    logger.info(`Raw sleepData from fitbit API: ${JSON.stringify(sleepData)}`);
    const mappedResponse = await Promise.all((sleepData?.sleep ?? []).map(async (sleepLog) => {
      return await mapper.mapSleepLogs(userId, sleepLog, offsetFromUTCMillis);
    }));    
    return mappedResponse || [];
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getECGList(userId, accessToken, afterDate, offsetFromUTCMillis) {
  var url = config.fitbit.api_base_url + `/1/user/-/ecg/list.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
    params: {
      afterDate,
      sort: "desc",
      limit: 10,
      offset: 0,
    },
  };
  try {
    const response = await axios(options);
    const ecgReadings = response.data?.ecgReadings || [];
    const mappedResponse = await Promise.all((ecgReadings ?? []).map(async (ecgLog) => {
      return await mapper.mapECGLogs(userId, ecgLog, offsetFromUTCMillis);
    }));    
    return mappedResponse || [];
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getCoreTempList(userId, accessToken, startDate, offsetFromUTCMillis) {
  const url =
    config.fitbit.api_base_url + `/1/user/-/temp/core/date/${startDate}/today.json`;

  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const tempCore = response.data?.tempCore || [];
    const mappedResponse = await Promise.all(tempCore.map(async (log) => {
      return await mapper.mapTempCore(userId, log, offsetFromUTCMillis);
    }));  
    return mappedResponse;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getActivitySummaryByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url + `/1/user/-/activities/date/${date}.json`;

  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };

  try {
    const response = await axios(options);
    const activitySummary = response.data;
    const mappedActivitySummary = await mapper.mapActivitySummary(
      userId,
      date,
      activitySummary.summary,
      offsetFromUTCMillis
    );
    return mappedActivitySummary;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return {};
  }
}

async function getWeightByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url +
    `/1/user/-/body/log/weight/date/${date}.json`;

  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const weightData = response.data;
    const weightLogs = await mapper.mapWeightByDate(userId, weightData.weight[0], offsetFromUTCMillis);
    const bmiLogs = await mapper.mapBMIByDate(userId, weightData.weight[0], offsetFromUTCMillis);
    return { weightLogs, bmiLogs };
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return {};
  }
}

async function getFatByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url +
    `/1/user/-/body/log/fat/date/${date}.json`;

  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const fatData = response.data;
    return await mapper.mapFatByDate(userId, fatData.fat[0], offsetFromUTCMillis);
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return {};
  }
}

async function getWaterByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url +
    `/1/user/-/foods/log/water/date/${date}.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const waterData = response.data;
    const mappedWaterData = await mapper.mapWaterByDate(userId, date, waterData, offsetFromUTCMillis);
    return mappedWaterData;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return {};
  }
}

async function getIntradayHeartRate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url +
    `/1/user/-/activities/heart/date/${date}/1d/15min.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const heartRateData = response.data;
    const restingHeartRate = await mapper.mapRestingHeartRateByDate(
      userId,
      heartRateData?.["activities-heart"]?.[0],
      offsetFromUTCMillis
    );
    const heartRateLogs = await mapper.mapHeartRateByDate(
      userId, heartRateData, offsetFromUTCMillis
    );
    return {restingHeartRate, heartRateLogs};
  } catch (error) {
    logger.warn(JSON.stringify(error));
    return {};
  }
}

async function getHRVByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url + `/1/user/-/hrv/date/${date}.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const HRVData = response.data;
    const mappedHRVData = await mapper.mapHRVByDate(userId, HRVData, offsetFromUTCMillis);
    return mappedHRVData;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return {};
  }
}

async function getSpo2ByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url + `/1/user/-/spo2/date/${date}.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const spo2Data = response.data;
    const mappedSpo2Data = await mapper.mapSpo2ByDate(userId, spo2Data, offsetFromUTCMillis);
    return mappedSpo2Data;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return {};
  }
}

async function getVo2ByDate(userId, accessToken, date, offsetFromUTCMillis) {
  var url =
    config.fitbit.api_base_url + `/1/user/-/cardioscore/date/${date}.json`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
  };
  try {
    const response = await axios(options);
    const vo2Data = response.data;
    const mappedVo2Data = await mapper.mapVo2ByDate(userId, vo2Data?.cardioScore?.[0], offsetFromUTCMillis);
    return mappedVo2Data;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error?.response?.data));
    return {};
  }
}

async function getTimeBasedTrackersData(userId, accessToken, lastSynced, offsetFromUTCMillis) {
  const afterTime = lastSynced.toISOString().split(".")[0]
  const startDate = lastSynced.toISOString().split('T')[0]
  logger.info(`getTimeBasedTrackersData() | startDate: ${startDate} | afterTime: ${afterTime}`);
  const TimeBasedTrackersData = {
    activityList: await getActivityList(userId, accessToken, afterTime, offsetFromUTCMillis),
    sleepLogs: await getSleepList(userId, accessToken, afterTime, offsetFromUTCMillis),
    ecgLogs: await getECGList(userId, accessToken, afterTime, offsetFromUTCMillis),
    tempLogs: await getCoreTempList(userId, accessToken, startDate, offsetFromUTCMillis),
  };
  return TimeBasedTrackersData;
}

async function getDayBasedTrackersData(userId, accessToken, lastSynced, offsetFromUTCMillis) {
  const date = lastSynced.toISOString().split("T")[0];
  const { weightLogs, bmiLogs } =  await getWeightByDate(userId, accessToken, date, offsetFromUTCMillis)
  const { heartRateLogs, restingHeartRate } = await getIntradayHeartRate(userId, accessToken, date, offsetFromUTCMillis)
  const dayBasedTrackersData = {
    activitySummary: await getActivitySummaryByDate(userId, accessToken, date, offsetFromUTCMillis),
    weightLogs, bmiLogs,
    waterLogs: await getWaterByDate(userId, accessToken, date, offsetFromUTCMillis),
    fatLogs: await getFatByDate(userId, accessToken, date, offsetFromUTCMillis),
    hrvLogs: await getHRVByDate(userId, accessToken, date, offsetFromUTCMillis),
    spo2Logs: await getSpo2ByDate(userId, accessToken, date, offsetFromUTCMillis),
    vo2Logs: await getVo2ByDate(userId, accessToken, date, offsetFromUTCMillis),
    heartRateLogs, restingHeartRate 
  };
  return dayBasedTrackersData;
}

const tracker = {
  getTimeBasedTrackersData,
  getDayBasedTrackersData,
};

module.exports = { tracker };
const { getStaticSources, getStaticDevices } = require("../../utils/staticData");

async function getLogMetadata(userId) {
  const now = new Date().toISOString();
  const sources = await getStaticSources();
  const devices = await getStaticDevices();
  const sourceId = sources.Dexcom.id;
  const sourceName = sources.Dexcom.name;
  const deviceId = devices.filter(x => x.sourceId == sourceId)[0]?.id || 21;
  const deviceName = devices.filter(x => x.id == deviceId)[0]?.name || 'Dexcom device';
  return {
    userId, createdAt: now, updatedAt: now,
    sourceId, sourceName, deviceId, deviceName
  }
}

async function mapEGVS(userId, egvs, unit) {
  const metaData = await getLogMetadata(userId);
  const data = egvs.map((ele) => {
    const timestamp = new Date(ele.systemTime)
    return {
      timestamp: timestamp.toISOString(),
      value: ele.value,
      unit,
      trend: ele.trend,
      ...metaData
    };
  });
  return data;
}

const mapper = { mapEGVS };

module.exports = { mapper };

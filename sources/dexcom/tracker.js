const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log: logger } = require("../../utils/logger");
const { mapper } = require("./mappers");

const apiUrl = config.dexcom.api_base_url + "/v2/users/self";

async function getEGVS(accessToken, startDate, endDate, userId) {
  const egvsURL = `${apiUrl}/egvs`;

  const options = {
    method: "GET",
    url: egvsURL,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      startDate,
      endDate,
    },
  };

  try {
    const response = await axios(options);
    const mappedEGVS = await mapper.mapEGVS(userId, response?.data?.egvs, response?.data?.unit);
    return mappedEGVS;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error.response.data));
    logger.warn(JSON.stringify(error.response.headers));
  }
  return [];
}

async function getDataDateRange(accessToken) {
  const dataDateRangeURL = `${apiUrl}/dataRange`;

  const options = {
    method: "GET",
    url: dataDateRangeURL,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios(options);
    return response.data;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error.response.data));
    logger.warn(JSON.stringify(error.response.headers));
  }
}

async function getCalibrations(accessToken, startDate, endDate) {
  const calibrationURL = `${apiUrl}/calibrations`;

  const options = {
    method: "GET",
    url: calibrationURL,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    params: {
      startDate,
      endDate,
    },
  };

  try {
    const response = await axios(options);
    return response.data;
  } catch (error) {
    logger.warn(JSON.stringify(error));
    logger.warn(JSON.stringify(error.response.data));
    logger.warn(JSON.stringify(error.response.headers));
  }
}

const tracker = {
  getCalibrations,
  getEGVS,
  getDataDateRange,
};

module.exports = tracker;

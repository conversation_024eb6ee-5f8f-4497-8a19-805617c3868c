const { roundOffNumber, getDuration } = require("../../utils/helpers");
const { getStaticSources, getStaticDevices } = require("../../utils/staticData");

async function getLogMetadata(userId) {
  const now = new Date().toISOString();
  const sources = await getStaticSources();
  const devices = await getStaticDevices();
  const sourceId = sources.Oura.id;
  const sourceName = sources.Oura.name;
  const deviceId = devices.filter(x => x.sourceId == sourceId)[0]?.id || 61;
  const deviceName = devices.filter(x => x.id == deviceId)[0]?.name || 'Oura device';
  return {
    userId, createdAt: now, updatedAt: now,
    sourceId, sourceName, deviceId, deviceName
  }
}

async function mapSleepLogs(userId, sleepLog) {
  if (!sleepLog || Object.keys(sleepLog)?.length === 0 || !sleepLog?.total_sleep_duration || !sleepLog.day || !sleepLog.bedtime_end)
    return {};
  const metaData = await getLogMetadata(userId);
  const startTime = new Date(sleepLog?.bedtime_start) 
  const endTime = new Date(sleepLog?.bedtime_end)
  const dayOfSleepUTC = new Date(`${sleepLog.day}T00:00:00${sleepLog.bedtime_end.slice(-6)}`).toISOString();

  // Modifying the summary object 
  const levelSummary = {
    "deep": {
      "count": 0,
      "seconds": sleepLog.deep_sleep_duration,
      "thirtyDayAverageMinutes": 0
    },
    "wake": {
      "count": 0,
      "seconds": sleepLog.awake_time,
      "thirtyDayAverageMinutes": 0
    },
    "light": {
      "count": 0,
      "seconds": sleepLog.light_sleep_duration,
      "thirtyDayAverageMinutes": 0
    },
    "rem": {
      "count": 0,
      "seconds": sleepLog.rem_sleep_duration,
      "thirtyDayAverageMinutes": 0
    }
  }

  const doc = {
    logId: sleepLog.id || null,
    duration: sleepLog.total_sleep_duration,
    timeInBed: sleepLog.time_in_bed,
    efficiency: sleepLog.efficiency,
    startTime: startTime.toISOString(),
    endTime: endTime.toISOString(),
    timestamp: dayOfSleepUTC, //rather than bedtime_end, timestamp will 12AM of day of sleep
    secondsAwake: sleepLog.awake_time,
    levels: {
      data: [],
      summary: levelSummary,
    },
    unit: "sec",
    ...metaData
  };
  return doc;
}

async function mapHeartRate(userId, heartRateData, offsetFromUTCMillis) {
  if (heartRateData.length === 0)
    return {};
  const metaData = await getLogMetadata(userId);
  
  const timestamp = new Date(heartRateData[0].timestamp);
  // timestamp is not adjusted according to offsetFromUTCMillis, as data is in GMT directly
  // timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);

  let intraday = [];
  for (let i = 0; i < heartRateData.length; i++) {
    if(heartRateData[i].bpm) {
      intraday.push({
        timestamp: new Date(heartRateData[i].timestamp).toISOString(),
        value: heartRateData[i].bpm
      });  
    }
  }
  const doc = {
    logId: heartRateData.id || null,  // No logId available for heartRate data
    unit: "bpm",
    timestamp: timestamp.toISOString(),
    heartRateZones: [], intraday,
    ...metaData
  };
  return doc;
}

async function mapRestingHeartRate(userId, heartRateData) {
  const restingHeartRate = heartRateData?.readiness?.contributors?.resting_heart_rate || null;
  if (Object.keys(heartRateData)?.length === 0 || !restingHeartRate)
    return {};
  const metaData = await getLogMetadata(userId);
  const doc = {
    logId: heartRateData.id || null,
    timestamp: new Date(heartRateData.bedtime_end).toISOString(),
    value: restingHeartRate,
    unit: "bpm",
    ...metaData
  };
  return doc;
}

async function mapHRV(userId, log) {
  const metaData = await getLogMetadata(userId);
  const doc = {
    logId: log.id || null,
    timestamp: new Date(log.bedtime_end).toISOString(),
    dailyRmssd: roundOffNumber(log.average_hrv, 2),
    unit: "ms",
    ...metaData
  };
  return doc;
}

async function mapActivitySummary(userId, activitySummary) {
  if (!activitySummary || Object.keys(activitySummary)?.length === 0)
    return {};
  const timestamp = new Date(activitySummary.timestamp).toISOString();
  const metaData = await getLogMetadata(userId);
  const doc = {
    logId: activitySummary.id || null,
    timestamp,
    steps: activitySummary.steps,
    elevation: activitySummary?.elevation || 0,
    floors: activitySummary?.floors || 0,
    distance: roundOffNumber(((activitySummary?.equivalent_walking_distance || 0) / 1000), 2),
    activityCalories: {
      bmrCalories: (activitySummary?.total_calories || 0) - (activitySummary?.active_calories || 0),
      activeCalories: activitySummary?.active_calories || 0,
      totalCalories: activitySummary?.total_calories || 0
    },
    activityTime: {
      lightlyActiveSeconds: roundOffNumber(activitySummary?.low_activity_time || 0, 2),
      fairlyActiveSeconds: roundOffNumber(activitySummary?.medium_activity_time || 0, 2),
      veryActiveSeconds: roundOffNumber(activitySummary?.high_activity_time || 0, 2),
      totalActiveSeconds: roundOffNumber((activitySummary?.low_activity_time || 0) + (activitySummary?.medium_activity_time || 0) + (activitySummary?.high_activity_time || 0), 2),
    },
    unit: "kcal",
    ...metaData
  };
  return doc;
}

async function mapActivityList(userId, activityLog) {
  if (!activityLog || Object.keys(activityLog)?.length === 0)
    return {};
  const timestamp = new Date(activityLog.start_datetime).toISOString();
  const duration = getDuration(activityLog.start_datetime, activityLog.end_datetime);
  const metaData = await getLogMetadata(userId);
  const doc = {
    logId: activityLog.id || null,
    timestamp,
    activityName: activityLog?.activity,
    duration,
    calories: roundOffNumber(activityLog?.calories || 0),
    unit: "kcal",
    ...metaData
  };
  return doc;
}

async function mapSpo2ByDate(userId, spo2Data, offsetFromUTCMillis) {
  if (!spo2Data || Object.keys(spo2Data)?.length === 0 || !spo2Data?.spo2_percentage?.average)
    return {};
  const timestamp = new Date(spo2Data.day);
  timestamp.setTime(timestamp.getTime() - offsetFromUTCMillis);
  const metaData = await getLogMetadata(userId);
  const doc = {
    logId: spo2Data.id || null,
    timestamp: timestamp.toISOString(),
    value: roundOffNumber(spo2Data?.spo2_percentage?.average, 0),
    unit: "%",
    ...metaData
  };
  return doc;
}

const mapper = {
  mapSleepLogs,
  mapActivitySummary,
  mapActivityList,
  mapHeartRate,
  mapRestingHeartRate,
  mapHRV,
  mapSpo2ByDate,
};

module.exports = { mapper };

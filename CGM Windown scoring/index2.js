// ===== CONFIGURABLE PARAMETERS =====
const RECOVERY_THRESHOLD_PERCENT = 10;

const MAX_SPIKE_DELTA = 30;
const MAX_AUC_ABOVE_BASELINE = 6000;
const MAX_RECOVERY_TIME_MIN = 120;
const MAX_CV_PERCENT = 36;

const WEIGHT_DELTA = 30;
const WEIGHT_AUC = 30;
const WEIGHT_RECOVERY = 25;
const WEIGHT_CV = 15;

function getTimeDiffInMinutes(later, earlier) {
  return ((later.getTime() - earlier.getTime()) / 60000).toFixed(2);
}

function calculateBaseline(glucoseData, mealStartTime) {
  const preMealReadings = glucoseData.filter(({ timestamp }) => timestamp < mealStartTime);

  if (preMealReadings.length === 0) return null;

  const total = preMealReadings.reduce((sum, pt) => sum + pt.glucose, 0);
  return (total / preMealReadings.length).toFixed(2);
}

function calculateAUC(glucoseData) {
  let auc = 0;
  for (let i = 1; i < glucoseData.length; i++) {
    const { glucose: g1, timestamp: t1 } = glucoseData[i - 1];
    const { glucose: g2, timestamp: t2 } = glucoseData[i];
    const duration = getTimeDiffInMinutes(t1, t2);
    auc += ((g1 + g2) / 2) * duration;
  }
  return auc.toFixed(2);
}

function calculateRecoveryTime(glucoseData, baseline, mealStartTime) {
  const threshold = baseline * (1 + RECOVERY_THRESHOLD_PERCENT / 100);
  for (let i = glucoseData.length - 1; i >= 0; i--) {
    const reading = glucoseData[i];
    if (reading.glucose <= threshold) {
      const minutesAfterMeal = getTimeDiffInMinutes(reading.timestamp, mealStartTime);
      if (minutesAfterMeal > 0) return minutesAfterMeal;
    }
  }  
  return getTimeDiffInMinutes(glucoseData[0].timestamp, mealStartTime);
}

function calculateCoefficientOfVariation(glucoseData) {
  const values = glucoseData.map(pt => pt.glucose);
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const stdDev = Math.sqrt(values.reduce((sum, val) => sum + (val - mean) ** 2, 0) / values.length);
  return ((stdDev / mean) * 100).toFixed(2);
}

function scoreGlucoseResponse({ delta, aucAboveBaseline, recoveryTime, cv }) {
  const deltaScore = WEIGHT_DELTA * Math.max(0, 1 - delta / MAX_SPIKE_DELTA);
  const aucScore = WEIGHT_AUC * Math.max(0, 1 - aucAboveBaseline / MAX_AUC_ABOVE_BASELINE);
  const recoveryScore = WEIGHT_RECOVERY * Math.max(0, 1 - recoveryTime / MAX_RECOVERY_TIME_MIN);
  const cvScore = WEIGHT_CV * Math.max(0, 1 - cv / MAX_CV_PERCENT);
 
  const totalScore = deltaScore + aucScore + recoveryScore + cvScore;
 
  return {
    finalScore: +(totalScore / 10).toFixed(2),
    totalScore: +totalScore.toFixed(2),
    breakdown: {
      deltaScore: +deltaScore.toFixed(2),
      aucScore: +aucScore.toFixed(2),
      recoveryScore: +recoveryScore.toFixed(2),
      cvScore: +cvScore.toFixed(2),
    },
  };
}

function analyzeGlucoseResponse(glucoseData, mealStartTime) {
  // 1. baseline
  const baseline = calculateBaseline(glucoseData, mealStartTime);

  // 2. delta
  const maxGlucose = Math.max(...glucoseData.map((pt) => pt.glucose));
  const delta = (maxGlucose - baseline).toFixed(2);

  // 3. auc
  const aucTotal = calculateAUC(glucoseData);
  const totalDuration = getTimeDiffInMinutes(glucoseData[0].timestamp, glucoseData[glucoseData.length - 1].timestamp);
  const aucAboveBaseline = (aucTotal - baseline * totalDuration).toFixed(2);

  // 4. recovery time
  const recoveryTime = calculateRecoveryTime(glucoseData, baseline, mealStartTime);

  // 5. cv
  const cv = calculateCoefficientOfVariation(glucoseData);

  console.log('baseline: ' + baseline);
  console.log('maxGlucose: '+ maxGlucose);
  console.log('delta: ' + delta);
  console.log('aucAboveBaseline: ' + aucAboveBaseline);
  console.log('recoveryTime: ' + recoveryTime);
  console.log('cv: ' + cv);
  console.log('-----------------------------------------------------');

  const scoreDoc = scoreGlucoseResponse({ delta, aucAboveBaseline, recoveryTime, cv });
  return scoreDoc;
}

const fs = require('fs');
const path = require('path');

// Read glucose data from bg1.json
const rawData = fs.readFileSync(path.join(__dirname, 'bg1.json'));
const jsonData = JSON.parse(rawData);
// Flatten all arrays from all keys into one array
const glucoseData = Object.values(jsonData)
  .flat()
  .map(entry => ({
    timestamp: new Date(entry.timestamp),
    glucose: entry.value
  }))
  .sort((a, b) => a.timestamp - b.timestamp);

// Set mealStartTime to the timestamp of the first reading (or adjust as needed)
const mealStartTime = new Date("2025-06-11T08:05:35.653Z");

console.log('-----------------------------------------------------');
const response = analyzeGlucoseResponse(glucoseData, mealStartTime);
console.log(response);
console.log('-----------------------------------------------------');

module.exports = {
  analyzeGlucoseResponse,
  calculateBaseline,
  calculateAUC,
  calculateRecoveryTime,
  calculateCoefficientOfVariation,
  scoreGlucoseResponse
};

version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 12
    commands:
      - npm install
      - npm install -g bestzip
      - bestzip archive.zip .
      - aws s3 cp archive.zip s3://healthtechgate-codepipeline-files/lambda-trackers-dev.zip
      - aws lambda update-function-code --function-name trackers-dev --s3-bucket healthtechgate-codepipeline-files --s3-key lambda-trackers-dev.zip
      # Set NODE_ENV for sandbox environment
      # Note: Other environment variables should be set via Terraform/CloudFormation or AWS Console
      - aws lambda update-function-configuration --function-name trackers-dev --environment "Variables={NODE_ENV=sandbox}"
      - current_version=$(aws lambda list-versions-by-function --function-name trackers-dev --query 'Versions[-1].Version' --output text --region us-east-1)
      - echo $current_version
      - delete_version=$(expr $current_version - 1)
      - echo $delete_version
      - sleep 20
      - aws lambda publish-version --function-name trackers-dev
      - new_version=$(aws lambda list-versions-by-function --function-name trackers-dev --query 'Versions[-1].Version' --output text --region us-east-1)
      - echo $new_version
      - aws lambda update-alias --name provisioned --function-name trackers-dev --function-version $new_version
      - sleep 20
      - aws lambda delete-function --function-name trackers-dev --region us-east-1 --qualifier $delete_version
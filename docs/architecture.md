# 20Degrees Trackers - Architecture Documentation

## Overview

The 20Degrees Trackers system is a health data tracking and aggregation platform that integrates with various health tracking devices and services. It collects, processes, and analyzes health data from multiple sources to provide users with insights about their health and wellness.

## System Architecture

The system follows a microservices architecture pattern with a Node.js Express backend that can run both as a standalone server and as an AWS Lambda function.

```mermaid
graph TD
    Client[Client Applications] --> API[API Gateway]
    API --> Trackers[Trackers Service]
    Trackers --> OS[(OpenSearch Database)]
    Trackers --> S3[(AWS S3)]
    Trackers --> SQS[(AWS SQS)]
    
    Trackers --> FitbitAPI[Fitbit API]
    Trackers --> DexcomAPI[Dexcom API]
    Trackers --> OuraAPI[Oura API]
    
    SQS --> TargetComputation[Target Computation Service]
    SQS --> NotificationService[Notification Service]
    
    subgraph "External Data Sources"
        FitbitAPI
        DexcomAPI
        OuraAPI
    end
```

## Core Components

### 1. API Layer

The system exposes RESTful APIs for client applications to interact with the service. The API endpoints are organized into:

- Authenticated endpoints (`/trackers/api/v1/*`)
- Public endpoints (`/trackers/*`)

Authentication is handled via JWT tokens validated by the middleware.

### 2. Controllers

Controllers handle the HTTP requests and responses. Key controllers include:

- **Device Controllers**: Manage connections to external health tracking devices
- **Tracker Controllers**: Handle health data tracking and retrieval
- **Source Controllers**: Manage data sources (Fitbit, Dexcom, Oura)
- **Target Controllers**: Handle health targets and achievements
- **Sync Controllers**: Manage data synchronization with external services

### 3. Services

Services contain the business logic and interact with the data layer. Key services include:

- **Tracker Service**: Manages health data tracking
- **Device Service**: Handles device connections and management
- **User Service**: Manages user data and profiles
- **Target Service**: Handles health targets and achievements
- **Logs Service**: Manages health data logs

### 4. Data Sources

The system integrates with multiple external health tracking services:

- **Fitbit**: Activity, sleep, heart rate, etc.
- **Dexcom**: Continuous glucose monitoring
- **Oura**: Sleep, activity, readiness

Each source has its own authentication, data retrieval, and data mapping modules.

### 5. Data Storage

The system uses:

- **OpenSearch**: Primary database for storing user data, device connections, and health metrics
- **AWS S3**: Storage for large data sets and logs
- **AWS SQS**: Message queuing for asynchronous processing

## Data Flow

```mermaid
sequenceDiagram
    participant User
    participant API as API Gateway
    participant Trackers as Trackers Service
    participant Source as External Source (Fitbit/Dexcom/Oura)
    participant DB as OpenSearch
    participant Queue as SQS Queue
    
    User->>API: Connect device request
    API->>Trackers: Forward request
    Trackers->>Source: OAuth authorization
    Source-->>Trackers: Authorization code
    Trackers->>Source: Request access token
    Source-->>Trackers: Access & refresh tokens
    Trackers->>DB: Store connection details
    Trackers-->>User: Connection successful
    
    Note over User,Queue: Data Synchronization Flow
    
    User->>API: Sync data request
    API->>Trackers: Forward request
    Trackers->>Source: Request health data
    Source-->>Trackers: Health data
    Trackers->>Trackers: Process & transform data
    Trackers->>DB: Store processed data
    Trackers->>Queue: Trigger target computation
    Queue-->>Trackers: Target computation complete
    Trackers-->>User: Sync complete
```

## Database Schema

The system uses OpenSearch as its primary database with the following indices:

```mermaid
erDiagram
    USER ||--o{ DEVICES : has
    USER ||--o{ TRACKERS : configures
    USER ||--o{ TARGETS : sets
    USER ||--o{ HEALTH_DATA : generates
    
    USER {
        string userId
        string state
        string source
        int sourceId
        string sourceUserId
        array trackers
        string accessToken
        string refreshToken
        string expiresAt
        datetime createdAt
        datetime updatedAt
    }
    
    DEVICES {
        string userId
        array connectedDevices
        datetime updatedAt
    }
    
    TRACKERS {
        string userId
        array defaultDevices
        datetime updatedAt
    }
    
    TARGETS {
        string userId
        int targetId
        float value
        string unit
        datetime createdAt
    }
    
    HEALTH_DATA {
        string userId
        int sourceId
        string sourceName
        int deviceId
        string deviceName
        float value
        string unit
        datetime timestamp
        datetime createdAt
    }
```

## Authentication & Authorization

The system uses OAuth 2.0 for authenticating with external services:

1. User initiates connection to a health service (Fitbit, Dexcom, Oura)
2. System redirects to the service's OAuth authorization page
3. User authorizes the application
4. Service redirects back with an authorization code
5. System exchanges the code for access and refresh tokens
6. Tokens are stored securely and used for subsequent API calls

## Deployment Architecture

The system is deployed as an AWS Lambda function with the following architecture:

```mermaid
graph TD
    Client[Client Applications] --> APIGW[API Gateway]
    APIGW --> Lambda[Lambda Function]
    Lambda --> OS[(OpenSearch)]
    Lambda --> S3[(S3 Bucket)]
    Lambda --> SQS[(SQS Queue)]
    
    SQS --> OtherLambdas[Other Lambda Functions]
    
    subgraph "AWS Cloud"
        APIGW
        Lambda
        OS
        S3
        SQS
        OtherLambdas
    end
```

## Scaling & Performance

- **Serverless Architecture**: The system uses AWS Lambda for automatic scaling based on demand
- **Message Queuing**: Asynchronous processing via SQS for handling large data volumes
- **Data Aggregation**: Efficient data storage and retrieval patterns for health metrics
- **Caching**: Static data caching for improved performance

## Security Considerations

- **JWT Authentication**: Secure API access
- **OAuth Token Management**: Secure storage and refresh of OAuth tokens
- **Data Encryption**: Sensitive data is encrypted at rest and in transit
- **Access Control**: Fine-grained access control for user data

# 20Degrees Trackers Documentation

This folder contains comprehensive documentation for the 20Degrees Trackers project.

## Documentation Files

- [Architecture Documentation](./architecture.md): Detailed information about the system architecture, components, data flow, and technical design.
- [Project Documentation](./project.md): Overview of the project, features, modules, API endpoints, and development information.
- [Data Models Documentation](./data-models.md): Detailed information about the data models, database schema, and data relationships.
- [API Documentation](./api.md): Comprehensive documentation of all API endpoints, request/response formats, and authentication.

## Diagrams

The documentation includes Mermaid diagrams for visualizing:

- System Architecture
- Data Flow
- Database Schema
- Deployment Architecture
- Module Structure
- Synchronization Process

## Getting Started

To view the documentation:

1. Open the Markdown files in a Markdown viewer that supports Mermaid diagrams
2. For the best experience, use a tool like VS Code with the Markdown Preview Enhanced extension

## Contributing to Documentation

When updating the documentation:

1. Keep diagrams up-to-date with the current system architecture
2. Ensure API endpoints are accurately documented
3. Update module descriptions when new features are added
4. Maintain the same formatting and style throughout the documentation

# 20Degrees Trackers - API Documentation

## Overview

The 20Degrees Trackers system exposes RESTful APIs for client applications to interact with the service. The API endpoints are organized into:

- Authenticated endpoints (`/trackers/api/v1/*`)
- Public endpoints (`/trackers/*`)

Authentication is handled via JWT tokens validated by the middleware.

## Authentication

### JWT Authentication

All authenticated endpoints require a valid JWT token in the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

The JWT token is validated by the `jwtValidator` middleware.

## API Endpoints

### User Endpoints

#### Get User Details

```
GET /trackers/api/v1/whoami
```

Returns the current user's details.

**Response:**

```json
{
  "success": true,
  "data": {
    "userId": "string",
    "userGuid": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string"
  }
}
```

### Source Endpoints

#### Get All Sources

```
GET /trackers/api/v1/sources
```

Returns all available data sources.

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "number",
      "name": "string",
      "displayName": "string",
      "description": "string",
      "trackers": ["string"],
      "isActive": "boolean"
    }
  ]
}
```

#### Get Source by ID

```
GET /trackers/api/v1/sources/:id
```

Returns a specific data source by ID.

**Parameters:**

- `id`: Source ID

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "number",
    "name": "string",
    "displayName": "string",
    "description": "string",
    "trackers": ["string"],
    "isActive": "boolean"
  }
}
```

### Device Endpoints

#### Get Connected Devices

```
GET /trackers/api/v1/devices
```

Returns all devices connected to the user's account.

**Response:**

```json
{
  "success": true,
  "data": {
    "userId": "string",
    "connectedDevices": [
      {
        "id": "number",
        "trackers": [
          {
            "trackerId": "number",
            "isEnabled": "boolean"
          }
        ],
        "isConnected": "boolean",
        "lastSynced": "string"
      }
    ]
  }
}
```

#### Get Device by ID

```
GET /trackers/api/v1/devices/:id
```

Returns a specific device by ID.

**Parameters:**

- `id`: Device ID

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "number",
    "name": "string",
    "description": "string",
    "sourceId": "number",
    "isConnected": "boolean",
    "lastSynced": "string",
    "trackers": [
      {
        "trackerId": "number",
        "isEnabled": "boolean"
      }
    ]
  }
}
```

#### Connect SDK Device

```
POST /trackers/api/v1/devices
```

Connects a new device to the user's account.

**Request Body:**

```json
{
  "id": "number",
  "trackers": [
    {
      "trackerId": "number",
      "isEnabled": "boolean"
    }
  ]
}
```

**Response:**

```json
{
  "success": true,
  "message": "Device connected successfully",
  "data": {
    "insertedId": "string"
  }
}
```

### Tracker Endpoints

#### Get Default Trackers

```
GET /trackers/api/v1/trackers
```

Returns the user's default trackers configuration.

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "category": "string",
      "subCategories": [
        {
          "id": "number",
          "name": "string",
          "deviceId": "number",
          "defaultTarget": "number",
          "lastLog": {
            "targetValue": "number",
            "currentValue": "number",
            "unit": "string"
          }
        }
      ]
    }
  ]
}
```

#### Update Default Trackers

```
PATCH /trackers/api/v1/trackers
```

Updates the user's default trackers configuration.

**Request Body:**

```json
{
  "trackerId": "number",
  "deviceId": "number"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Log entry saved",
  "data": {
    "insertedId": "string"
  }
}
```

### Fitbit Endpoints

#### Get Fitbit Metadata

```
GET /trackers/api/v1/fitbit/metadata
```

Returns Fitbit connection metadata including the OAuth URL.

**Response:**

```json
{
  "success": true,
  "message": "Visit Authorise URL to proceed with OAuth Login.",
  "data": {
    "authorizeUrl": "string"
  }
}
```

#### Fitbit OAuth Callback

```
GET /trackers/fitbit/callback
```

Handles the OAuth callback from Fitbit.

**Query Parameters:**

- `code`: Authorization code
- `state`: State parameter for security

**Response:**

Redirects to the client application with success or error message.

#### Manual Sync with Fitbit

```
GET /trackers/api/v1/fitbit/sync
```

Triggers a manual synchronization with Fitbit.

**Response:**

```json
{
  "success": true,
  "message": "Sync completed successfully",
  "data": {
    "syncedData": {
      "waterLogs": ["object"],
      "activitySummary": ["object"],
      "sleepLogs": ["object"],
      "heartRateLogs": ["object"]
    }
  }
}
```

### Dexcom Endpoints

Similar to Fitbit endpoints, with Dexcom-specific functionality.

### Oura Endpoints

Similar to Fitbit endpoints, with Oura-specific functionality.

### Target Endpoints

#### Get All Targets

```
GET /trackers/api/v1/targets
```

Returns all targets for the user.

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "targetId": "number",
      "value": "number",
      "unit": "string",
      "createdAt": "string"
    }
  ]
}
```

#### Get Target by ID

```
GET /trackers/api/v1/targets/:targetId
```

Returns a specific target by ID.

**Parameters:**

- `targetId`: Target ID

**Response:**

```json
{
  "success": true,
  "data": {
    "targetId": "number",
    "value": "number",
    "unit": "string",
    "createdAt": "string"
  }
}
```

#### Set Target by ID

```
POST /trackers/api/v1/targets
```

Sets a target for the user.

**Request Body:**

```json
{
  "targetId": "number",
  "value": "number"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Target set successfully",
  "data": {
    "insertedId": "string"
  }
}
```

### Target Achievement Endpoints

#### Get All Target Achievements

```
GET /trackers/api/v1/targets/achievements
```

Returns all target achievements for the user.

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "targetId": "number",
      "currentValue": "number",
      "isMet": "boolean",
      "date": "string"
    }
  ]
}
```

#### Get Target Achievement by ID

```
GET /trackers/api/v1/targets/achievements/:targetId
```

Returns target achievements for a specific target.

**Parameters:**

- `targetId`: Target ID

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "targetId": "number",
      "currentValue": "number",
      "isMet": "boolean",
      "date": "string"
    }
  ]
}
```

### Wellness Score Endpoints

#### Get Wellness Score

```
GET /trackers/api/v1/wellness-score
```

Returns the user's wellness score.

**Response:**

```json
{
  "success": true,
  "data": {
    "score": "number",
    "content": {
      "title": "string",
      "subtitle": "string",
      "media": {
        "src": "string",
        "imageUrl": "string",
        "type": "string",
        "mediaOverlayText": "string"
      }
    }
  }
}
```

### Recommendations Endpoints

#### Get Recommendations

```
GET /trackers/api/v1/recommendations
```

Returns personalized recommendations for the user.

**Response:**

```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "title": "string",
        "description": "string",
        "imageUrl": "string",
        "actionUrl": "string"
      }
    ]
  }
}
```

## Error Handling

All API endpoints return a standard error response format:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "details": "Error details"
  }
}
```

Common HTTP status codes:

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

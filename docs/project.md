# 20Degrees Trackers - Project Documentation

## Project Overview

The 20Degrees Trackers project is a health data tracking and aggregation platform that integrates with various health tracking devices and services. It enables users to connect their health tracking devices, synchronize health data, set health targets, and track their progress.

## Key Features

- **Multi-device Integration**: Connect with Fitbit, Dexcom, Oura, and other health tracking devices
- **Health Data Tracking**: Track various health metrics including:
  - Activity (steps, distance, calories)
  - Sleep (duration, quality)
  - Heart rate (resting, active)
  - Blood glucose levels
  - Blood pressure
  - Weight and body composition
  - Temperature
  - Body measurements
  - Mindfulness sessions
- **Target Setting**: Set health targets and track progress
- **Data Synchronization**: Automatic and manual synchronization with connected devices
- **Wellness Score**: Calculate overall wellness score based on health metrics
- **Recommendations**: Provide personalized health recommendations

## Technology Stack

- **Backend**: Node.js with Express
- **Database**: OpenSearch (AWS Elasticsearch Service)
- **Cloud Services**: AWS Lambda, S3, SQS
- **Authentication**: JWT for API authentication, OAuth for device connections
- **API Integration**: Fitbit API, Dexcom API, Oura API

## Project Structure

```
/
├── controllers/         # HTTP request handlers
├── environment/         # Environment configuration
├── middleware/          # Express middleware
├── models/              # Data models and validation schemas
├── service/             # Business logic services
├── sources/             # External API integrations
│   ├── fitbit/
│   ├── dexcom/
│   └── oura/
├── utils/               # Utility functions
├── index.js             # Application entry point
└── package.json         # Project dependencies
```

## Core Modules

### Trackers

The Trackers module manages the health metrics that users can track. Each tracker represents a specific health metric (e.g., steps, sleep, heart rate).

```mermaid
graph TD
    A[Trackers] --> B[Default Trackers]
    A --> C[Tracker Categories]
    A --> D[Tracker Details]

    B --> E[User Default Devices]
    C --> F[Activity]
    C --> G[Sleep]
    C --> H[Vitals]
    C --> I[Body]

    F --> F1[Steps]
    F --> F2[Distance]
    F --> F3[Calories]

    G --> G1[Sleep Duration]
    G --> G2[Sleep Quality]

    H --> H1[Heart Rate]
    H --> H2[Blood Pressure]
    H --> H3[Blood Glucose]

    I --> I1[Weight]
    I --> I2[Body Fat]
    I --> I3[Measurements]
```

### Devices

The Devices module manages the connections to external health tracking devices and services.

```mermaid
graph TD
    A[Devices] --> B[Connected Devices]
    A --> C[Device Management]
    A --> D[Device Sync]

    B --> E[Fitbit]
    B --> F[Dexcom]
    B --> G[Oura]
    B --> H[Manual Entry]

    C --> I[Connect Device]
    C --> J[Disconnect Device]
    C --> K[Update Device]

    D --> L[Manual Sync]
    D --> M[Auto Sync]
    D --> N[Sync History]
```

### Targets

The Targets module manages health targets that users can set and track.

```mermaid
graph TD
    A[Targets] --> B[Target Setting]
    A --> C[Target Tracking]
    A --> D[Target Achievement]

    B --> E[Daily Targets]
    B --> F[Weekly Targets]

    C --> G[Progress Tracking]
    C --> H[Target History]

    D --> I[Achievement Calculation]
    D --> J[Achievement History]
```

### Data Synchronization

The Data Synchronization module manages the synchronization of health data from external sources.

```mermaid
sequenceDiagram
    participant User
    participant System
    participant ExternalSource

    User->>System: Request Sync
    System->>ExternalSource: Request Data
    ExternalSource-->>System: Return Data
    System->>System: Process Data
    System->>System: Store Data
    System->>System: Calculate Targets
    System-->>User: Sync Complete

    Note over System,ExternalSource: Automatic Sync
    ExternalSource->>System: Webhook Notification
    System->>ExternalSource: Request Data
    ExternalSource-->>System: Return Data
    System->>System: Process Data
    System->>System: Store Data
    System->>System: Calculate Targets
```

## API Endpoints

### Authentication Endpoints

- `GET /trackers/api/v1/whoami`: Get current user details

### Device Endpoints

- `GET /trackers/api/v1/devices`: Get connected devices
- `GET /trackers/api/v1/devices/:id`: Get device by ID
- `POST /trackers/api/v1/devices`: Connect a new device

### Tracker Endpoints

- `GET /trackers/api/v1/trackers`: Get default trackers
- `PATCH /trackers/api/v1/trackers`: Update default trackers

### Source Endpoints

- `GET /trackers/api/v1/sources`: Get available sources
- `GET /trackers/api/v1/sources/:id`: Get source by ID

### Fitbit Endpoints

- `GET /trackers/api/v1/fitbit/metadata`: Get Fitbit metadata
- `GET /trackers/fitbit/callback`: Fitbit OAuth callback
- `GET /trackers/api/v1/fitbit/sync`: Manual sync with Fitbit

### Dexcom Endpoints

- `GET /trackers/api/v1/dexcom/metadata`: Get Dexcom metadata
- `GET /trackers/dexcom/callback`: Dexcom OAuth callback
- `GET /trackers/api/v1/dexcom/sync`: Manual sync with Dexcom

### Oura Endpoints

- `GET /trackers/api/v1/oura/metadata`: Get Oura metadata
- `GET /trackers/oura/callback`: Oura OAuth callback
- `GET /trackers/api/v1/oura/sync`: Manual sync with Oura

### Target Endpoints

- `GET /trackers/api/v1/targets`: Get all targets
- `GET /trackers/api/v1/targets/:targetId`: Get target by ID
- `POST /trackers/api/v1/targets`: Set target by ID
- `GET /trackers/api/v1/targets/achievements`: Get all target achievements
- `GET /trackers/api/v1/targets/achievements/:targetId`: Get target achievements by ID

### Wellness Score Endpoints

- `GET /trackers/api/v1/wellness-score`: Get wellness score

### Recommendations Endpoints

- `GET /trackers/api/v1/recommendations`: Get recommendations

## Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Configure environment variables:
   - Copy `environment/env/.env.example` to `environment/env/.env.development`
   - Edit the `.env.development` file with your configuration values
4. Start the development server: `npm run start:dev`

## Deployment

The application is deployed as an AWS Lambda function:

1. Build the application: `npm install && bestzip archive.zip .`
2. Upload to S3: `aws s3 cp archive.zip s3://healthtechgate-codepipeline-files/lambda-trackers.zip`
3. Update Lambda function: `aws lambda update-function-code --function-name trackers --s3-bucket healthtechgate-codepipeline-files --s3-key lambda-trackers.zip`
4. Publish new version: `aws lambda publish-version --function-name trackers`

## Environment Configuration

The application uses environment variables for configuration with the following hierarchy:

1. Command line arguments (highest priority)
2. Environment variables
3. `.env` files (local development only)

**Local Development:**
- Environment variables are loaded from `.env` files in `environment/env/`
- Different environments use different `.env` files (`.env.development`, `.env.sandbox`, `.env.production`)

**Deployed Environments:**
- Environment variables are set directly in the AWS Lambda function configuration
- No `.env` files are used in production deployments

## Testing

Currently, the project does not have automated tests. The `npm test` command is configured but not implemented.

## Future Enhancements

- Implement automated testing
- Add support for additional health tracking devices
- Enhance data visualization capabilities
- Implement machine learning for personalized recommendations
- Improve performance and scalability

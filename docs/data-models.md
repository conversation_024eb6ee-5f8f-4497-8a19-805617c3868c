# 20Degrees Trackers - Data Models Documentation

## Overview

The 20Degrees Trackers system uses OpenSearch (AWS Elasticsearch Service) as its primary database. This document describes the data models and schema used in the system.

## Database Indices

The system uses the following OpenSearch indices:

```mermaid
graph TD
    A[OpenSearch Indices] --> B[User Data]
    A --> C[Health Data]
    A --> D[Configuration Data]
    
    B --> B1[user]
    B --> B2[user_profiles]
    
    C --> C1[water]
    C --> C2[activity]
    C --> C3[activity_summary]
    C --> C4[sleep]
    C --> C5[bp]
    C --> C6[bg]
    C --> C7[egvs]
    C --> C8[spo2]
    C --> C9[heart_rate]
    C --> C10[resting_heart_rate]
    C --> C11[hrv]
    C --> C12[vo2]
    C --> C13[ecg]
    C --> C14[height]
    C --> C15[weight]
    C --> C16[fat]
    C --> C17[bmi]
    C --> C18[temp]
    C --> C19[waist_size]
    C --> C20[hip_size]
    C --> C21[chest_size]
    C --> C22[arm_size]
    C --> C23[quad_size]
    C --> C24[mindfulness]
    
    D --> D1[devices]
    D --> D2[trackers]
    D --> D3[triggers]
    D --> D4[user_targets]
    D --> D5[user_targets_achievements]
    D --> D6[user_forms]
    D --> D7[target_cron]
    D --> D8[user_wellness_score]
    D --> D9[user_recommendations]
```

## Core Data Models

### User Model

The User model stores information about user connections to external health tracking services.

```json
{
  "userId": "string",
  "state": "string",
  "source": "string",
  "sourceId": "string",
  "sourceUserId": "string",
  "trackers": ["string"],
  "accessToken": "string",
  "refreshToken": "string",
  "expiresAt": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

### Devices Model

The Devices model stores information about user's connected devices.

```json
{
  "userId": "string",
  "connectedDevices": [
    {
      "id": "number",
      "trackers": [
        {
          "trackerId": "number",
          "isEnabled": "boolean"
        }
      ],
      "isConnected": "boolean",
      "lastSynced": "string"
    }
  ],
  "updatedAt": "string"
}
```

### Trackers Model

The Trackers model stores user's default tracker configurations.

```json
{
  "userId": "string",
  "defaultDevices": [
    {
      "trackerId": "number",
      "deviceId": "number"
    }
  ],
  "updatedAt": "string"
}
```

### Targets Model

The Targets model stores user's health targets.

```json
{
  "userId": "string",
  "targetId": "number",
  "value": "number",
  "unit": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

### Health Data Models

Each health metric has its own data model. Here are some examples:

#### General Model (used for simple metrics)

```json
{
  "userId": "string",
  "sourceId": "number",
  "sourceName": "string",
  "deviceId": "number",
  "deviceName": "string",
  "timestamp": "string",
  "value": "number",
  "unit": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### Blood Pressure Model

```json
{
  "userId": "string",
  "sourceId": "number",
  "sourceName": "string",
  "deviceId": "number",
  "deviceName": "string",
  "timestamp": "string",
  "systole": "number",
  "diastole": "number",
  "unit": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### ECG Model

```json
{
  "userId": "string",
  "sourceId": "number",
  "sourceName": "string",
  "deviceId": "number",
  "deviceName": "string",
  "timestamp": "string",
  "averageHeartRate": "integer",
  "resultClassification": "string",
  "waveformSamples": ["integer"],
  "samplingFrequencyHz": "string",
  "scalingFactor": "integer",
  "numberOfWaveformSamples": "integer",
  "leadNumber": "integer",
  "unit": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### Sleep Model

```json
{
  "userId": "string",
  "sourceId": "number",
  "sourceName": "string",
  "deviceId": "number",
  "deviceName": "string",
  "timestamp": "string",
  "startTime": "string",
  "endTime": "string",
  "duration": "number",
  "efficiency": "number",
  "isMainSleep": "boolean",
  "stages": {
    "deep": "number",
    "light": "number",
    "rem": "number",
    "wake": "number"
  },
  "createdAt": "string",
  "updatedAt": "string"
}
```

## Data Validation

The system uses Ajv (Another JSON Validator) for data validation. Each data model has a corresponding JSON schema that defines the required fields and their types.

Example of a JSON schema for the General model:

```json
{
  "type": "object",
  "required": ["value", "timestamp"],
  "properties": {
    "timestamp": {"type": "string", "format": "date-time"},
    "value": {"type": "number"},
    "unit": {"type": "string"}
  }
}
```

## Data Relationships

```mermaid
erDiagram
    USER ||--o{ DEVICES : has
    USER ||--o{ TRACKERS : configures
    USER ||--o{ TARGETS : sets
    USER ||--o{ HEALTH_DATA : generates
    
    DEVICES ||--o{ TRACKERS : supports
    TRACKERS ||--o{ HEALTH_DATA : categorizes
    TARGETS ||--o{ TARGETS_ACHIEVED : achieves
    
    USER {
        string userId
    }
    
    DEVICES {
        string userId
        array connectedDevices
    }
    
    TRACKERS {
        string userId
        array defaultDevices
    }
    
    TARGETS {
        string userId
        int targetId
        float value
    }
    
    HEALTH_DATA {
        string userId
        int trackerId
        float value
    }
    
    TARGETS_ACHIEVED {
        string userId
        int targetId
        float currentValue
        boolean isMet
    }
```

## Data Flow

1. **Data Collection**: Health data is collected from external sources (Fitbit, Dexcom, Oura) or manual entry
2. **Data Transformation**: Raw data is transformed into the appropriate data model
3. **Data Validation**: Transformed data is validated against the JSON schema
4. **Data Storage**: Validated data is stored in the corresponding OpenSearch index
5. **Target Calculation**: Health data is used to calculate target achievements
6. **Wellness Score Calculation**: Health data and target achievements are used to calculate wellness score

## Data Aggregation

Some health metrics are aggregated for analysis and reporting:

- **Daily Aggregation**: Metrics like steps, water intake, and calories are aggregated daily
- **Weekly Aggregation**: Some targets are tracked on a weekly basis
- **Monthly Aggregation**: Long-term trends are analyzed monthly

## Data Retention

The system retains health data indefinitely for historical analysis and trend identification. Users can request data deletion in compliance with privacy regulations.

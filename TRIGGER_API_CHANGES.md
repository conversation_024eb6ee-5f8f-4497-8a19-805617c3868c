# Trigger API Changes - New Response Format

## Overview
Updated the GET `/triggers` endpoints (`getAllTriggers` and `getTriggerById`) to return data in a new format that includes calculated CGM scores and details. The scoring is calculated for the window from `startedAt` to `expiresAt` using CGM data.

## Changes Made

### 1. Created CGM Scoring Utility (`utils/cgmScoring.js`)
- **Purpose**: Calculate glucose response scores based on CGM data
- **Key Functions**:
  - `analyzeGlucoseResponse()`: Main scoring function that calculates final score and breakdown
  - `calculateCGMDetails()`: Calculates peak CGM, unit, and time above target
  - Helper functions for baseline, AUC, recovery time, and coefficient of variation

### 2. Updated Controller (`controllers/triggers.js`)
- **Modified Functions**:
  - `getAllTriggers`: Now returns array of trigger objects with scoring
  - `getTriggerById`: Returns single trigger object with scoring
- **Key Changes**:
  - Fetches CGM data for each trigger window using `logsService.getAllLogsByDateRange()`
  - Calculates scores using the new scoring utility
  - Formats response to match the stubbed response structure
  - Adds `graphIconURL` to triggers and metadata

## New Response Format

### getAllTriggers Response
```json
{
  "success": true,
  "message": "All trigger logs for user: {userId} for duration: {days}",
  "data": [
    {
      "startedAt": "2024-01-19T13:56:40.567Z",
      "triggers": [
        {
          "mealLogId": "65aa7f9734dd1317cac9c4b8",
          "mealTags": [...],
          "triggeredAt": "2024-01-19T13:56:40.567Z",
          "graphIconURL": "https://example.com/default_icon.png"
        }
      ],
      "details": {
        "peakCGM": 145,
        "unit": "mg/dl",
        "aboveTargetSecs": 5100,
        "score": {
          "final": 6.62,
          "total": 66.21,
          "breakdown": {
            "delta": 0,
            "auc": 54.94,
            "recovery": 0,
            "cv": 11.27
          }
        }
      },
      "expiresAt": "2024-01-19T15:56:40.567Z",
      "_id": "IdkCIo0BumX87aigbqEl",
      "metadata": [...]  // Optional, only if metadata exists
    }
  ]
}
```

### getTriggerById Response
```json
{
  "success": true,
  "message": "Found trigger log id: {triggerId}",
  "data": {
    "startedAt": "2024-01-19T13:56:40.567Z",
    "triggers": [...],
    "details": {
      "peakCGM": 145,
      "unit": "mg/dl",
      "aboveTargetSecs": 5100,
      "score": {...}
    },
    "expiresAt": "2024-01-19T15:56:40.567Z",
    "_id": "IdkCIo0BumX87aigbqEl",
    "metadata": [...]  // Optional
  }
}
```

## Scoring Algorithm

The scoring algorithm evaluates glucose response based on four key metrics:

1. **Delta (30% weight)**: Glucose spike from baseline
2. **AUC (30% weight)**: Area under curve above baseline
3. **Recovery (25% weight)**: Time to return to baseline + 10%
4. **CV (15% weight)**: Coefficient of variation (glucose variability)

### Scoring Parameters
- Max spike delta: 30 mg/dl
- Max AUC above baseline: 6000
- Max recovery time: 120 minutes
- Max CV: 36%

### Score Calculation
- Each metric is scored from 0-1 based on how close it is to the maximum threshold
- Final score = (total score / 10) for a 0-10 scale
- Lower glucose spikes, faster recovery, and less variability result in higher scores

## Implementation Notes

1. **CGM Data Source**: Uses `config.INDEX.egvs` to fetch CGM data
2. **Time Window**: Scoring calculated for data between `startedAt` and `expiresAt`
3. **Meal Start Time**: Uses `startedAt` as the meal start time for baseline calculation
4. **Default Icon**: Uses tracker image URL or falls back to default icon
5. **Error Handling**: Returns zero scores if no CGM data or insufficient pre-meal data

## Backward Compatibility

- The API endpoints maintain the same URL structure
- Response format is completely new (not backward compatible)
- All existing functionality for creating, updating, and managing triggers remains unchanged
- Only GET endpoints (`getAllTriggers` and `getTriggerById`) have been modified

## Dependencies

- Existing services: `logsService`, `triggerService`, `trackersService`
- New utility: `utils/cgmScoring.js`
- CGM data must be available in the `egvs` index for scoring to work properly

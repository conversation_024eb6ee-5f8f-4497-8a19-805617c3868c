{"type": "object", "required": ["userId", "triggers"], "properties": {"userId": {"type": "string"}, "triggers": {"type": "array", "items": {"type": "object", "properties": {"mealLogId": {"type": "string"}, "triggeredAt": {"type": "string", "format": "date-time"}, "mealTags": {"type": "array"}}}}, "metadata": {"type": "array", "items": {"type": "object", "properties": {"eventId": {"type": "string"}, "eventType": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}, "startedAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": true}
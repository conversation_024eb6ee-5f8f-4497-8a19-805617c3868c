{"type": "object", "required": ["timestamp", "steps"], "properties": {"timestamp": {"type": "string", "format": "date-time"}, "steps": {"type": "integer"}, "elevation": {"type": "integer"}, "floors": {"type": "integer"}, "distance": {"type": "number"}, "standHours": {"type": "number"}, "standMinutes": {"type": "number"}, "activityCalories": {"type": "object", "properties": {"totalCalories": {"type": "integer"}, "bmrCalories": {"type": "integer"}, "activeCalories": {"type": "integer"}}}, "activityTime": {"type": "object", "properties": {"totalActiveSeconds": {"type": "integer"}, "lightlyActiveSeconds": {"type": "integer"}, "fairlyActiveSeconds": {"type": "integer"}, "veryActiveSeconds": {"type": "integer"}}}}}
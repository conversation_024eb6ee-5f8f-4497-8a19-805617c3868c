{"type": "object", "required": ["connectedDevices"], "properties": {"connectedDevices": {"type": "array", "items": {"type": "object", "required": ["id", "trackers", "isConnected"], "properties": {"id": {"type": "number"}, "deviceName": {"type": "string"}, "trackers": {"type": "array", "items": {"type": "object", "required": ["trackerId"], "properties": {"trackerId": {"type": "integer"}}}}, "isConnected": {"type": "boolean"}, "serialNumber": {"type": "array"}}}}}}
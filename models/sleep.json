{"type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}, "duration": {"type": "integer"}, "efficiency": {"type": "integer"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}, "timeInBed": {"type": "integer"}, "secondsToFallAsleep": {"type": "integer"}, "secondsAsleep": {"type": "integer"}, "secondsAwake": {"type": "integer"}, "secondsAfterWakeup": {"type": "integer"}, "isMainSleep": {"type": "boolean"}, "levels": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "required": ["dateTime", "seconds"], "properties": {"dateTime": {"type": "string", "format": "date-time"}, "level": {"type": "string"}, "seconds": {"type": "integer"}}}}, "summary": {"type": "object", "properties": {"deep": {"type": "object", "properties": {"count": {"type": "integer"}, "seconds": {"type": "integer"}, "thirtyDayAvgSeconds": {"type": "integer"}}, "required": ["count", "seconds", "thirtyDayAvgSeconds"]}, "light": {"type": "object", "properties": {"count": {"type": "integer"}, "seconds": {"type": "integer"}, "thirtyDayAvgSeconds": {"type": "integer"}}, "required": ["count", "seconds", "thirtyDayAvgSeconds"]}, "rem": {"type": "object", "properties": {"count": {"type": "integer"}, "seconds": {"type": "integer"}, "thirtyDayAvgSeconds": {"type": "integer"}}, "required": ["count", "seconds", "thirtyDayAvgSeconds"]}, "wake": {"type": "object", "properties": {"count": {"type": "integer"}, "seconds": {"type": "integer"}, "thirtyDayAvgSeconds": {"type": "integer"}}, "required": ["count", "seconds", "thirtyDayAvgSeconds"]}}, "required": ["deep", "light", "rem", "wake"]}}}}}
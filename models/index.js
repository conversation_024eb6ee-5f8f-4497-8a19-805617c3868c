const metadata = require("./metadata.json")

function mergeMetdata(tracker) {
    tracker.properties = { ...(tracker?.properties || []), ...metadata.properties }
    tracker.required = [...(tracker?.required || []), ...metadata.required ]
    return tracker
}

const general = mergeMetdata(require("./general.json"))
const activitySummary = mergeMetdata(require("./activitySummary.json"))
const activity = mergeMetdata(require("./activity.json"))
const sleep = mergeMetdata(require("./sleep.json"))
const bp = mergeMetdata(require("./bp.json"))
const CGM = mergeMetdata(require("./CGM.json"))
const spo2 = mergeMetdata(require("./spo2.json"))
const vo2 = mergeMetdata(require("./vo2.json"))
const heartRate = mergeMetdata(require("./heartRate.json"))
const hrv = mergeMetdata(require("./hrv.json"))
const ecg = mergeMetdata(require("./ecg.json"))
const mindfulness = mergeMetdata(require("./mindfulness.json"))

module.exports = {
    1: general,
    2: general, // Water [Date]
    3: activitySummary, // activitySummary [Date]
    4: activity,  // activity
    5: sleep, // sleep [Date]
    6: bp, // BP
    7: general, // Blood Glucose
    8: CGM, // CGM/EGVS
    9: spo2, // SPo2 [Date]
    10: heartRate, // Heart Rate [Date]
    11: hrv, // HRV [Date]
    12: vo2, // VO2 [Date]
    13: ecg, // ECG
    14: general, // Height [Date]
    15: general, // Weight [Date]
    16: general, // Fat [Date]
    17: general, // BMI [Date]
    18: general, // Temperature
    19: general, // Waist Size [Date]
    20: general, // Hip [Date]
    21: general, // Chest [Date]
    22: general, // Arm [Date]
    23: general, // Quad [Date]
    24: mindfulness, // Mindfulness
    25: general, // Resting Heart Rate 
}

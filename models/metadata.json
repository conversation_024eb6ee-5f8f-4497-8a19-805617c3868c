{"type": "object", "required": ["userId", "sourceId", "sourceName", "deviceId", "deviceName", "createdAt", "updatedAt"], "properties": {"userId": {"type": "string"}, "sourceId": {"type": "integer"}, "sourceName": {"type": "string"}, "deviceId": {"type": "integer"}, "deviceName": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}
{"type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}, "activityName": {"type": "string"}, "activityTypeId": {"type": "string"}, "duration": {"type": "integer"}, "activeDuration": {"type": "integer"}, "calories": {"type": "integer"}, "steps": {"type": "integer"}, "distance": {"type": "integer"}, "unit": {"type": "string"}, "elevationGain": {"type": "integer"}}}
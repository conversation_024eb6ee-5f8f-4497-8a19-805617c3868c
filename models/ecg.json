{"type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}, "averageHeartRate": {"type": "integer"}, "resultClassification": {"type": "string"}, "waveformSamples": {"type": "array", "items": {"type": "integer"}}, "samplingFrequencyHz": {"type": "string"}, "scalingFactor": {"type": "integer"}, "numberOfWaveformSamples": {"type": "integer"}, "leadNumber": {"type": "integer"}, "unit": {"type": "string"}}}
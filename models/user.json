{"type": "object", "required": ["userId", "state"], "properties": {"userId": {"type": "string"}, "state": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "sourceUserId": {"type": "string"}, "trackers": {"type": "array", "items": {"type": "string"}}, "accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "expiresAt": {"type": "string"}, "createdAt": {"type": "string", "format": "date"}, "updatedAt": {"type": "string", "format": "date"}}, "additionalProperties": false}
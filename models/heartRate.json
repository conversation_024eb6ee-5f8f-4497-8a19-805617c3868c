{"type": "object", "required": ["timestamp"], "properties": {"timestamp": {"type": "string", "format": "date-time"}, "unit": {"type": "string"}, "heartRateZones": {"type": "array", "items": {"type": "object", "properties": {"caloriesOut": {"type": "integer"}, "max": {"type": "integer"}, "min": {"type": "integer"}, "duration": {"type": "integer"}, "zone": {"type": "string"}}}, "minItems": 1}, "intraday": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "value": {"type": "integer"}}}, "minItems": 0}}}